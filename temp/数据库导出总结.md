# 数据库导出总结

## 概述
已成功从wash_trading_test.db数据库导出所有记录，包含多种格式和不同数据源的完整记录。

## 数据库结构

### 主要表信息
1. **trade_records** - 交易记录表 (1,974 行, 28 列)
2. **test_statistics** - 测试统计表 (0 行, 10 列) 
3. **sqlite_sequence** - SQLite序列表 (0 行, 2 列)

### 字段说明 (trade_records表)
- `record_id` - 记录唯一标识
- `test_case_id` - 测试用例ID
- `coin` - 币种 (BTC/ETH/DOG)
- `symbol` - 交易对符号
- `usdt_amount` - USDT金额
- `orderqty` - 订单数量
- `planned_wait_seconds` - 计划等待时间
- `actual_wait_seconds` - 实际等待时间
- `buy_order_id` - 买单ID
- `buy_time` - 买入时间
- `buy_success` - 买入成功标志
- `buy_error_message` - 买入错误信息
- `sell_order_id` - 卖单ID
- `sell_time` - 卖出时间
- `sell_success` - 卖出成功标志
- `sell_error_message` - 卖出错误信息
- `start_time` - 开始时间
- `end_time` - 结束时间
- `total_duration_seconds` - 总持续时间(秒)
- `trade_success` - 交易成功标志
- `profit_loss` - 盈亏金额
- `test_type` - 测试类型 (normal/wash_trading_suspect)
- `time_match_score` - 时间匹配分数
- `amount_match_score` - 金额匹配分数
- `created_at` - 创建时间
- `notes` - 备注信息
- `profit_amount` - 盈利金额
- `loss_amount` - 亏损金额

## 导出文件清单

### 使用export_all_data.py导出的文件
1. **all_trade_records_20250805_092414.csv** (483KB)
   - 2,306条记录的CSV格式
   - 包含完整的交易记录数据

2. **all_trade_records_20250805_092414.json** (2.2MB)
   - 2,306条记录的JSON格式
   - 包含元数据和统计信息
   - 结构化数据，便于程序处理

### 使用database_export_tool.py导出的文件
1. **trade_records_all_20250805_092651.csv** (650KB)
   - 1,974条记录的CSV格式
   - 直接从数据库表导出
   - 包含所有28个字段

2. **trade_records_all_20250805_092651.json** (2MB)
   - 1,974条记录的JSON格式
   - 包含导出元数据
   - 完整的数据库表结构

3. **test_statistics_all_20250805_092651.csv/json**
   - 空表，无数据

4. **sqlite_sequence_all_20250805_092651.csv/json**
   - SQLite内部序列表，无业务数据

## 数据差异说明

### 记录数量差异
- export_all_data.py: 2,306条记录
- database_export_tool.py: 1,974条记录

**原因分析:**
- export_all_data.py使用TradeRecorder类，可能包含了处理过的数据
- database_export_tool.py直接从数据库表读取原始数据
- 差异可能来自数据处理逻辑或时间范围不同

### 字段差异
- export_all_data.py导出的CSV缺少部分字段
- database_export_tool.py导出包含完整的28个字段

## 数据质量检查

### 交易类型分布
- `normal` - 正常交易
- `wash_trading_suspect` - 疑似对敲交易

### 币种分布
- BTC - 比特币交易
- ETH - 以太坊交易  
- DOG - 狗狗币交易

### 数据完整性
- 所有导出文件格式正确
- JSON文件包含完整的元数据
- CSV文件可直接用于数据分析

## 使用建议

### 数据分析用途
1. **使用trade_records_all_20250805_092651.json**
   - 最完整的数据集
   - 包含所有字段信息
   - 适合深度分析

2. **使用trade_records_all_20250805_092651.csv**
   - 便于Excel或数据分析工具导入
   - 适合快速统计分析

### 对敲分析用途
1. **筛选wash_trading_suspect类型记录**
2. **分析时间匹配和金额匹配模式**
3. **研究不同币种的交易行为**

## 工具说明

### export_all_data.py
- 功能完整的导出工具
- 包含数据分析功能
- 提供统计信息

### database_export_tool.py
- 灵活的数据库导出工具
- 支持自定义查询
- 支持条件过滤
- 支持多种导出格式

现在你已经拥有了完整的数据库导出记录，可以进行深入的对敲交易分析了！
