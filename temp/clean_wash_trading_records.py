#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对敲记录清理脚本
移除notes为null的无效记录
"""

import json
import os
from datetime import datetime

def clean_wash_trading_records(input_file, output_file=None):
    """
    清理对敲记录，移除notes为null的记录
    
    Args:
        input_file (str): 输入JSON文件路径
        output_file (str): 输出JSON文件路径，如果为None则覆盖原文件
    """
    
    # 读取原始数据
    print(f"正在读取文件: {input_file}")
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 统计原始记录数
    original_count = len(data['records'])
    print(f"原始记录总数: {original_count}")
    
    # 过滤掉notes为null的记录
    valid_records = []
    removed_records = []
    
    for record in data['records']:
        if record.get('notes') is None:
            removed_records.append(record)
            print(f"移除记录: {record['record_id']} - notes为null")
        else:
            valid_records.append(record)
    
    # 更新数据
    data['records'] = valid_records
    
    # 更新统计信息
    cleaned_count = len(valid_records)
    removed_count = len(removed_records)
    
    print(f"\n清理结果:")
    print(f"有效记录数: {cleaned_count}")
    print(f"移除记录数: {removed_count}")
    print(f"移除比例: {removed_count/original_count*100:.2f}%")
    
    # 更新metadata中的记录总数
    if 'metadata' in data:
        data['metadata']['total_records'] = cleaned_count
        data['metadata']['cleaned_at'] = datetime.now().isoformat()
        data['metadata']['removed_null_notes_count'] = removed_count
    
    # 更新statistics中的记录总数
    if 'statistics' in data:
        data['statistics']['total_tests'] = cleaned_count
        data['statistics']['successful_tests'] = cleaned_count  # 假设所有有效记录都是成功的
    
    # 确定输出文件路径
    if output_file is None:
        # 创建备份
        backup_file = input_file.replace('.json', '_backup.json')
        print(f"\n创建备份文件: {backup_file}")
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(json.load(open(input_file, 'r', encoding='utf-8')), f, 
                     ensure_ascii=False, indent=2)
        output_file = input_file
    
    # 保存清理后的数据
    print(f"保存清理后的数据到: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    # 保存移除的记录到单独文件
    removed_file = output_file.replace('.json', '_removed_records.json')
    print(f"保存移除的记录到: {removed_file}")
    removed_data = {
        "metadata": {
            "removed_at": datetime.now().isoformat(),
            "total_removed": removed_count,
            "reason": "notes字段为null"
        },
        "removed_records": removed_records
    }
    with open(removed_file, 'w', encoding='utf-8') as f:
        json.dump(removed_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n清理完成！")
    return cleaned_count, removed_count

def main():
    """主函数"""
    input_file = "../对敲记录.json"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return
    
    try:
        # 执行清理
        cleaned_count, removed_count = clean_wash_trading_records(input_file)
        
        print(f"\n✅ 清理成功完成!")
        print(f"📊 最终统计:")
        print(f"   - 保留有效记录: {cleaned_count} 条")
        print(f"   - 移除无效记录: {removed_count} 条")
        print(f"   - 备份文件: 对敲记录_backup.json")
        print(f"   - 移除记录文件: 对敲记录_removed_records.json")
        
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
