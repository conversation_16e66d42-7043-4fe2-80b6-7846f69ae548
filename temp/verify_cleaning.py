#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证清理结果的脚本
"""

import json

def verify_cleaning():
    """验证清理结果"""
    
    # 检查清理后的文件
    print("🔍 验证清理后的对敲记录文件...")
    with open('../对敲记录.json', 'r', encoding='utf-8') as f:
        cleaned_data = json.load(f)
    
    # 统计notes为null的记录
    null_notes_count = 0
    total_records = len(cleaned_data['records'])
    
    for record in cleaned_data['records']:
        if record.get('notes') is None:
            null_notes_count += 1
            print(f"⚠️  发现notes为null的记录: {record['record_id']}")
    
    print(f"\n📊 清理后文件统计:")
    print(f"   - 总记录数: {total_records}")
    print(f"   - notes为null的记录数: {null_notes_count}")
    
    if null_notes_count == 0:
        print("✅ 清理成功！没有发现notes为null的记录")
    else:
        print(f"❌ 清理不完整！仍有 {null_notes_count} 条notes为null的记录")
    
    # 检查移除的记录文件
    print(f"\n🗑️  验证移除的记录文件...")
    with open('../对敲记录_removed_records.json', 'r', encoding='utf-8') as f:
        removed_data = json.load(f)
    
    removed_count = len(removed_data['removed_records'])
    print(f"   - 移除的记录数: {removed_count}")
    
    # 验证所有移除的记录都是notes为null
    all_null = True
    for record in removed_data['removed_records']:
        if record.get('notes') is not None:
            print(f"⚠️  移除的记录中发现notes不为null: {record['record_id']}")
            all_null = False
    
    if all_null:
        print("✅ 移除的记录验证通过！所有记录的notes都为null")
    else:
        print("❌ 移除的记录验证失败！存在notes不为null的记录")
    
    # 检查备份文件
    print(f"\n💾 验证备份文件...")
    with open('../对敲记录_backup.json', 'r', encoding='utf-8') as f:
        backup_data = json.load(f)
    
    backup_total = len(backup_data['records'])
    expected_total = total_records + removed_count
    
    print(f"   - 备份文件记录数: {backup_total}")
    print(f"   - 预期记录数: {expected_total}")
    
    if backup_total == expected_total:
        print("✅ 备份文件验证通过！记录数匹配")
    else:
        print("❌ 备份文件验证失败！记录数不匹配")
    
    print(f"\n🎯 最终验证结果:")
    print(f"   - 原始记录: {backup_total} 条")
    print(f"   - 清理后记录: {total_records} 条")
    print(f"   - 移除记录: {removed_count} 条")
    print(f"   - 数据完整性: {'✅ 通过' if backup_total == total_records + removed_count else '❌ 失败'}")
    print(f"   - 清理效果: {'✅ 完全清理' if null_notes_count == 0 else '❌ 不完整'}")

if __name__ == "__main__":
    verify_cleaning()
