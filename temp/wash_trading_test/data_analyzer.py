#!/usr/bin/env python3
"""
对敲检测数据分析和报告生成器
分析测试结果，生成对敲检测相关的统计报告
"""

import json
import csv
import sqlite3
import math
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import statistics

from trade_recorder import TradeRecorder, TradeRecord


@dataclass
class WashTradingAnalysis:
    """对敲分析结果"""
    pair_id: str
    record_a: TradeRecord
    record_b: TradeRecord
    time_diff_seconds: float
    amount_diff_usdt: float
    amount_diff_percent: float
    time_match_score: float
    amount_match_score: float
    profit_hedge_score: float
    wash_score: float
    risk_level: str
    is_wash_trading: bool


class WashTradingDataAnalyzer:
    """对敲检测数据分析器"""
    
    def __init__(self, db_path: str = "wash_trading_test.db"):
        """初始化分析器"""
        self.db_path = db_path
        self.recorder = TradeRecorder(db_path)
        self.analysis_results = []
    
    def calculate_time_match_score(self, time_diff_seconds: float, time_window: float = 30.0) -> float:
        """计算时间匹配分数
        
        Args:
            time_diff_seconds: 时间差（秒）
            time_window: 时间窗口（秒）
            
        Returns:
            时间匹配分数 (0-1)
        """
        if time_diff_seconds >= time_window:
            return 0.0
        
        normalized_diff = time_diff_seconds / time_window
        return math.exp(-2 * normalized_diff)  # 指数衰减
    
    def calculate_amount_match_score(self, amount_a: float, amount_b: float) -> float:
        """计算金额匹配分数
        
        Args:
            amount_a: 金额A
            amount_b: 金额B
            
        Returns:
            金额匹配分数 (0-1)
        """
        if amount_a == 0 or amount_b == 0:
            return 0.0
        
        amount_diff = abs(amount_a - amount_b)
        max_amount = max(amount_a, amount_b)
        
        # 计算容差
        if max_amount <= 100:
            tolerance = max(max_amount * 0.10, 2.0)
        elif max_amount <= 1000:
            tolerance = max(max_amount * 0.05, 5.0)
        elif max_amount <= 10000:
            tolerance = min(max_amount * 0.03, 50.0)
        elif max_amount <= 100000:
            tolerance = min(max_amount * 0.02, 200.0)
        else:
            tolerance = max_amount * 0.015
        
        if amount_diff == 0:
            return 1.0
        elif amount_diff <= tolerance:
            return 1.0 - 0.2 * (amount_diff / tolerance)
        else:
            excess_ratio = (amount_diff - tolerance) / tolerance
            return 0.80 * math.exp(-excess_ratio * 0.75)
    
    def calculate_profit_hedge_score(self, profit_a: float, profit_b: float, 
                                   amount_a: float, amount_b: float) -> float:
        """计算盈亏对敲分数
        
        Args:
            profit_a: 盈亏A
            profit_b: 盈亏B
            amount_a: 交易金额A
            amount_b: 交易金额B
            
        Returns:
            盈亏对敲分数 (0-1)
        """
        # 由于测试中无法获取实际盈亏，这里使用模拟计算
        # 假设短时间交易的盈亏很小，接近对敲特征
        
        total_profit = profit_a + profit_b
        profit_sum = abs(profit_a) + abs(profit_b)
        
        if abs(total_profit) == 0:
            return 0.8  # 完全抵消
        
        if profit_sum == 0:
            return 0.5  # 无盈亏数据
        
        # 简化计算：假设短时间交易盈亏接近0
        return 0.7  # 默认中等对敲特征分数
    
    def calculate_wash_score(self, time_match_score: float, amount_match_score: float,
                           profit_hedge_score: float, duration_similarity: float = 0.5) -> float:
        """计算综合对敲分数
        
        Args:
            time_match_score: 时间匹配分数
            amount_match_score: 金额匹配分数
            profit_hedge_score: 盈亏对敲分数
            duration_similarity: 持仓时长相似度
            
        Returns:
            综合对敲分数 (0-1)
        """
        # 权重配置
        weights = {
            'profit_hedge': 0.4,
            'time_match': 0.25,
            'amount_match': 0.25,
            'duration': 0.1
        }
        
        return (profit_hedge_score * weights['profit_hedge'] +
                time_match_score * weights['time_match'] +
                amount_match_score * weights['amount_match'] +
                duration_similarity * weights['duration'])
    
    def classify_risk_level(self, wash_score: float, time_match_score: float,
                          profit_hedge_score: float) -> str:
        """分类风险等级
        
        Args:
            wash_score: 综合对敲分数
            time_match_score: 时间匹配分数
            profit_hedge_score: 盈亏对敲分数
            
        Returns:
            风险等级
        """
        if profit_hedge_score > 0.9 and time_match_score > 0.8:
            return 'Critical'
        elif wash_score > 0.85:
            return 'High'
        elif wash_score > 0.7:
            return 'Medium'
        elif wash_score > 0.5:
            return 'Low'
        else:
            return 'Minimal'
    
    def find_potential_wash_trading_pairs(self, time_window: float = 30.0) -> List[WashTradingAnalysis]:
        """查找潜在的对敲交易对
        
        Args:
            time_window: 时间窗口（秒）
            
        Returns:
            对敲分析结果列表
        """
        # 加载交易记录
        records = self.recorder.load_from_database()
        successful_records = [r for r in records if r.trade_success]
        
        print(f"📊 分析 {len(successful_records)} 条成功交易记录")
        
        analysis_results = []
        pair_id = 1
        
        # 按币种分组
        coin_groups = {}
        for record in successful_records:
            if record.coin not in coin_groups:
                coin_groups[record.coin] = []
            coin_groups[record.coin].append(record)
        
        # 在每个币种内查找潜在对敲对
        for coin, coin_records in coin_groups.items():
            print(f"🔍 分析 {coin} 币种的 {len(coin_records)} 条记录")
            
            for i in range(len(coin_records)):
                for j in range(i + 1, len(coin_records)):
                    record_a = coin_records[i]
                    record_b = coin_records[j]
                    
                    # 计算时间差
                    try:
                        time_a = datetime.fromisoformat(record_a.buy_time)
                        time_b = datetime.fromisoformat(record_b.buy_time)
                        time_diff = abs((time_a - time_b).total_seconds())
                    except:
                        continue
                    
                    # 只分析时间窗口内的交易对
                    if time_diff <= time_window:
                        # 计算各项分数
                        time_match_score = self.calculate_time_match_score(time_diff, time_window)
                        amount_match_score = self.calculate_amount_match_score(
                            record_a.usdt_amount, record_b.usdt_amount
                        )
                        profit_hedge_score = self.calculate_profit_hedge_score(
                            0, 0, record_a.usdt_amount, record_b.usdt_amount  # 假设盈亏为0
                        )
                        wash_score = self.calculate_wash_score(
                            time_match_score, amount_match_score, profit_hedge_score
                        )
                        
                        # 分类风险等级
                        risk_level = self.classify_risk_level(
                            wash_score, time_match_score, profit_hedge_score
                        )
                        
                        # 判断是否为对敲
                        is_wash_trading = wash_score > 0.7 and profit_hedge_score > 0.7
                        
                        # 计算金额差异
                        amount_diff_usdt = abs(record_a.usdt_amount - record_b.usdt_amount)
                        max_amount = max(record_a.usdt_amount, record_b.usdt_amount)
                        amount_diff_percent = (amount_diff_usdt / max_amount * 100) if max_amount > 0 else 0
                        
                        analysis = WashTradingAnalysis(
                            pair_id=f"{coin}_{pair_id}",
                            record_a=record_a,
                            record_b=record_b,
                            time_diff_seconds=time_diff,
                            amount_diff_usdt=amount_diff_usdt,
                            amount_diff_percent=amount_diff_percent,
                            time_match_score=time_match_score,
                            amount_match_score=amount_match_score,
                            profit_hedge_score=profit_hedge_score,
                            wash_score=wash_score,
                            risk_level=risk_level,
                            is_wash_trading=is_wash_trading
                        )
                        
                        analysis_results.append(analysis)
                        pair_id += 1
        
        # 按对敲分数排序
        analysis_results.sort(key=lambda x: x.wash_score, reverse=True)
        self.analysis_results = analysis_results
        
        return analysis_results
    
    def generate_analysis_report(self, analysis_results: List[WashTradingAnalysis] = None) -> Dict[str, Any]:
        """生成分析报告
        
        Args:
            analysis_results: 分析结果列表
            
        Returns:
            分析报告
        """
        if analysis_results is None:
            analysis_results = self.analysis_results
        
        if not analysis_results:
            return {'error': '没有分析结果'}
        
        # 基本统计
        total_pairs = len(analysis_results)
        wash_trading_pairs = [a for a in analysis_results if a.is_wash_trading]
        wash_trading_count = len(wash_trading_pairs)
        
        # 风险等级分布
        risk_distribution = {}
        for analysis in analysis_results:
            risk = analysis.risk_level
            if risk not in risk_distribution:
                risk_distribution[risk] = 0
            risk_distribution[risk] += 1
        
        # 币种分布
        coin_distribution = {}
        for analysis in analysis_results:
            coin = analysis.record_a.coin
            if coin not in coin_distribution:
                coin_distribution[coin] = {'total': 0, 'wash_trading': 0}
            coin_distribution[coin]['total'] += 1
            if analysis.is_wash_trading:
                coin_distribution[coin]['wash_trading'] += 1
        
        # 分数统计
        wash_scores = [a.wash_score for a in analysis_results]
        time_scores = [a.time_match_score for a in analysis_results]
        amount_scores = [a.amount_match_score for a in analysis_results]
        
        # 时间分布
        time_ranges = {
            '0-5s': 0, '5-15s': 0, '15-30s': 0, '30-60s': 0, '60s+': 0
        }
        for analysis in analysis_results:
            time_diff = analysis.time_diff_seconds
            if time_diff <= 5:
                time_ranges['0-5s'] += 1
            elif time_diff <= 15:
                time_ranges['5-15s'] += 1
            elif time_diff <= 30:
                time_ranges['15-30s'] += 1
            elif time_diff <= 60:
                time_ranges['30-60s'] += 1
            else:
                time_ranges['60s+'] += 1
        
        report = {
            'analysis_metadata': {
                'generated_at': datetime.now().isoformat(),
                'total_pairs_analyzed': total_pairs,
                'wash_trading_pairs_found': wash_trading_count,
                'wash_trading_rate': wash_trading_count / total_pairs * 100 if total_pairs > 0 else 0
            },
            'risk_distribution': risk_distribution,
            'coin_distribution': coin_distribution,
            'score_statistics': {
                'wash_score': {
                    'min': min(wash_scores) if wash_scores else 0,
                    'max': max(wash_scores) if wash_scores else 0,
                    'avg': statistics.mean(wash_scores) if wash_scores else 0,
                    'median': statistics.median(wash_scores) if wash_scores else 0
                },
                'time_match_score': {
                    'min': min(time_scores) if time_scores else 0,
                    'max': max(time_scores) if time_scores else 0,
                    'avg': statistics.mean(time_scores) if time_scores else 0
                },
                'amount_match_score': {
                    'min': min(amount_scores) if amount_scores else 0,
                    'max': max(amount_scores) if amount_scores else 0,
                    'avg': statistics.mean(amount_scores) if amount_scores else 0
                }
            },
            'time_distribution': time_ranges,
            'top_wash_trading_pairs': [
                {
                    'pair_id': a.pair_id,
                    'coin': a.record_a.coin,
                    'amount_a': a.record_a.usdt_amount,
                    'amount_b': a.record_b.usdt_amount,
                    'time_diff_seconds': a.time_diff_seconds,
                    'wash_score': a.wash_score,
                    'risk_level': a.risk_level
                }
                for a in analysis_results[:20]  # 前20个
            ]
        }
        
        return report
    
    def export_analysis_results(self, filename: str, format: str = 'json'):
        """导出分析结果
        
        Args:
            filename: 文件名
            format: 格式 ('json' 或 'csv')
        """
        if not self.analysis_results:
            print("❌ 没有分析结果可导出")
            return
        
        if format.lower() == 'json':
            report = self.generate_analysis_report()
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        elif format.lower() == 'csv':
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'pair_id', 'coin', 'amount_a', 'amount_b', 'time_diff_seconds',
                    'amount_diff_usdt', 'amount_diff_percent', 'time_match_score',
                    'amount_match_score', 'profit_hedge_score', 'wash_score',
                    'risk_level', 'is_wash_trading'
                ]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for analysis in self.analysis_results:
                    writer.writerow({
                        'pair_id': analysis.pair_id,
                        'coin': analysis.record_a.coin,
                        'amount_a': analysis.record_a.usdt_amount,
                        'amount_b': analysis.record_b.usdt_amount,
                        'time_diff_seconds': analysis.time_diff_seconds,
                        'amount_diff_usdt': analysis.amount_diff_usdt,
                        'amount_diff_percent': analysis.amount_diff_percent,
                        'time_match_score': analysis.time_match_score,
                        'amount_match_score': analysis.amount_match_score,
                        'profit_hedge_score': analysis.profit_hedge_score,
                        'wash_score': analysis.wash_score,
                        'risk_level': analysis.risk_level,
                        'is_wash_trading': analysis.is_wash_trading
                    })
        
        print(f"✅ 分析结果已导出到 {filename}")
    
    def print_analysis_summary(self):
        """打印分析摘要"""
        if not self.analysis_results:
            print("❌ 没有分析结果")
            return
        
        report = self.generate_analysis_report()
        
        print("📊 对敲检测分析报告")
        print("=" * 60)
        print(f"分析时间: {report['analysis_metadata']['generated_at']}")
        print(f"总交易对数: {report['analysis_metadata']['total_pairs_analyzed']}")
        print(f"疑似对敲对数: {report['analysis_metadata']['wash_trading_pairs_found']}")
        print(f"对敲检出率: {report['analysis_metadata']['wash_trading_rate']:.1f}%")
        print()
        
        print("🚨 风险等级分布:")
        for risk, count in report['risk_distribution'].items():
            percentage = count / report['analysis_metadata']['total_pairs_analyzed'] * 100
            print(f"  {risk}: {count} ({percentage:.1f}%)")
        print()
        
        print("🪙 币种分布:")
        for coin, data in report['coin_distribution'].items():
            wash_rate = data['wash_trading'] / data['total'] * 100 if data['total'] > 0 else 0
            print(f"  {coin}: {data['total']} 总对数, {data['wash_trading']} 疑似对敲 ({wash_rate:.1f}%)")
        print()
        
        print("📈 分数统计:")
        scores = report['score_statistics']
        print(f"  综合对敲分数: 平均 {scores['wash_score']['avg']:.3f}, 最高 {scores['wash_score']['max']:.3f}")
        print(f"  时间匹配分数: 平均 {scores['time_match_score']['avg']:.3f}")
        print(f"  金额匹配分数: 平均 {scores['amount_match_score']['avg']:.3f}")
        print()
        
        print("⏰ 时间分布:")
        for time_range, count in report['time_distribution'].items():
            percentage = count / report['analysis_metadata']['total_pairs_analyzed'] * 100
            print(f"  {time_range}: {count} ({percentage:.1f}%)")


def main():
    """主函数"""
    print("📊 对敲检测数据分析器")
    print("=" * 40)
    
    analyzer = WashTradingDataAnalyzer()
    
    while True:
        print("\n请选择:")
        print("1. 分析潜在对敲交易对")
        print("2. 查看分析摘要")
        print("3. 导出分析结果 (JSON)")
        print("4. 导出分析结果 (CSV)")
        print("5. 自定义时间窗口分析")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("🔍 开始分析潜在对敲交易对...")
            results = analyzer.find_potential_wash_trading_pairs()
            print(f"✅ 分析完成，找到 {len(results)} 个交易对")
            analyzer.print_analysis_summary()
        elif choice == '2':
            analyzer.print_analysis_summary()
        elif choice == '3':
            filename = input("请输入JSON文件名 (默认: wash_trading_analysis.json): ").strip()
            if not filename:
                filename = "wash_trading_analysis.json"
            analyzer.export_analysis_results(filename, 'json')
        elif choice == '4':
            filename = input("请输入CSV文件名 (默认: wash_trading_analysis.csv): ").strip()
            if not filename:
                filename = "wash_trading_analysis.csv"
            analyzer.export_analysis_results(filename, 'csv')
        elif choice == '5':
            try:
                time_window = float(input("请输入时间窗口（秒，默认30）: ") or "30")
                print(f"🔍 使用 {time_window} 秒时间窗口分析...")
                results = analyzer.find_potential_wash_trading_pairs(time_window)
                print(f"✅ 分析完成，找到 {len(results)} 个交易对")
                analyzer.print_analysis_summary()
            except ValueError:
                print("❌ 请输入有效数字")
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
