#!/usr/bin/env python3
"""
模块测试脚本
验证所有交易模块是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试基础模块
        from utils.config import load_config
        from utils.logger import setup_logger, get_logger
        from api.client import BipcClient
        print("✅ 基础模块导入成功")
        
        # 测试交易模块
        from btc_trader import btc_trade_sequence, usdt_to_btc_orderqty
        from eth_trader import eth_trade_sequence, usdt_to_eth_orderqty
        from dog_trader import dog_trade_sequence, usdt_to_dog_orderqty
        print("✅ 交易模块导入成功")
        
        # 测试统一接口
        from unified_trader import UnifiedTrader
        print("✅ 统一交易接口导入成功")
        
        # 测试其他模块
        from test_data_generator import WashTradingTestGenerator
        from trade_recorder import TradeRecorder
        from test_execution_engine import TestExecutionEngine
        from data_analyzer import WashTradingDataAnalyzer
        print("✅ 其他模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 导入异常: {e}")
        return False


def test_coin_conversions():
    """测试币种换算函数"""
    print("\n💰 测试币种换算...")
    
    try:
        from btc_trader import usdt_to_btc_orderqty, btc_orderqty_to_usdt
        from eth_trader import usdt_to_eth_orderqty, eth_orderqty_to_usdt
        from dog_trader import usdt_to_dog_orderqty, dog_orderqty_to_usdt
        
        # 测试BTC换算
        btc_usdt = 10000
        btc_qty = usdt_to_btc_orderqty(btc_usdt)
        btc_back = btc_orderqty_to_usdt(btc_qty)
        print(f"✅ BTC: {btc_usdt} USDT → {btc_qty} orderQty → {btc_back:.0f} USDT")
        
        # 测试ETH换算
        eth_usdt = 1000
        eth_qty = usdt_to_eth_orderqty(eth_usdt)
        eth_back = eth_orderqty_to_usdt(eth_qty)
        print(f"✅ ETH: {eth_usdt} USDT → {eth_qty} orderQty → {eth_back:.0f} USDT")
        
        # 测试DOG换算
        dog_usdt = 100
        dog_qty = usdt_to_dog_orderqty(dog_usdt)
        dog_back = dog_orderqty_to_usdt(dog_qty)
        print(f"✅ DOG: {dog_usdt} USDT → {dog_qty} orderQty → {dog_back:.0f} USDT")
        
        return True
        
    except Exception as e:
        print(f"❌ 换算测试失败: {e}")
        return False


def test_unified_trader():
    """测试统一交易接口"""
    print("\n🔄 测试统一交易接口...")
    
    try:
        from unified_trader import UnifiedTrader
        
        trader = UnifiedTrader()
        
        # 测试币种信息
        coins = trader.get_supported_coins()
        print(f"✅ 支持的币种: {coins}")
        
        # 测试金额验证
        for coin in coins:
            config = trader.get_coin_info(coin)
            if config:
                print(f"✅ {coin}: {config.min_usdt}-{config.max_usdt} USDT")
                
                # 测试换算
                test_amount = config.min_usdt * 2
                orderqty = trader.usdt_to_orderqty(coin, test_amount)
                back_amount = trader.orderqty_to_usdt(coin, orderqty)
                print(f"   换算测试: {test_amount} USDT → {orderqty} orderQty → {back_amount:.2f} USDT")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一交易接口测试失败: {e}")
        return False


def test_data_generator():
    """测试数据生成器"""
    print("\n🎲 测试数据生成器...")
    
    try:
        from test_data_generator import WashTradingTestGenerator
        
        generator = WashTradingTestGenerator(seed=42)
        
        # 生成少量测试用例
        test_cases = generator.generate_test_batch(10)
        print(f"✅ 生成了 {len(test_cases)} 个测试用例")
        
        # 显示前几个
        for i, case in enumerate(test_cases[:3], 1):
            print(f"   {i}. {case.description} ({case.test_type})")
        
        # 统计信息
        stats = generator.get_statistics(test_cases)
        print(f"✅ 统计信息: {stats['total_cases']} 总数, {stats['wash_trading_suspects']} 疑似对敲")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据生成器测试失败: {e}")
        return False


def test_trade_recorder():
    """测试交易记录器"""
    print("\n📝 测试交易记录器...")
    
    try:
        from trade_recorder import TradeRecorder
        
        recorder = TradeRecorder("test_wash_trading.db")
        
        # 模拟交易结果
        mock_trade_result = {
            'success': True,
            'coin': 'DOG',
            'usdt_amount': 10.0,
            'orderqty': 40,
            'buy_order_id': 'test_buy_123',
            'sell_order_id': 'test_sell_456',
            'start_time': '2025-08-01T10:00:00',
            'buy_time': '2025-08-01T10:00:01',
            'sell_time': '2025-08-01T10:00:11',
            'end_time': '2025-08-01T10:00:12',
            'planned_wait_seconds': 10,
            'actual_wait_seconds': 10.0,
            'total_duration_seconds': 12.0,
            'buy_result': {'success': True, 'order_id': 'test_buy_123'},
            'sell_result': {'success': True, 'order_id': 'test_sell_456'}
        }
        
        # 记录交易
        record = recorder.record_trade(mock_trade_result, 1, 'normal')
        print(f"✅ 记录了交易: {record.record_id}")
        
        # 获取统计
        stats = recorder.get_statistics()
        if stats:
            print(f"✅ 统计信息: {stats['total_tests']} 总数, {stats['successful_tests']} 成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易记录器测试失败: {e}")
        return False


def test_data_analyzer():
    """测试数据分析器"""
    print("\n📊 测试数据分析器...")
    
    try:
        from data_analyzer import WashTradingDataAnalyzer
        
        analyzer = WashTradingDataAnalyzer("test_wash_trading.db")
        
        # 测试评分函数
        time_score = analyzer.calculate_time_match_score(5.0, 30.0)
        amount_score = analyzer.calculate_amount_match_score(100.0, 105.0)
        profit_score = analyzer.calculate_profit_hedge_score(0.0, 0.0, 100.0, 105.0)
        wash_score = analyzer.calculate_wash_score(time_score, amount_score, profit_score)
        
        print(f"✅ 评分测试:")
        print(f"   时间匹配分数: {time_score:.3f}")
        print(f"   金额匹配分数: {amount_score:.3f}")
        print(f"   盈亏对敲分数: {profit_score:.3f}")
        print(f"   综合对敲分数: {wash_score:.3f}")
        
        # 风险等级
        risk_level = analyzer.classify_risk_level(wash_score, time_score, profit_score)
        print(f"   风险等级: {risk_level}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据分析器测试失败: {e}")
        return False


def test_config():
    """测试配置模块"""
    print("\n⚙️ 测试配置模块...")
    
    try:
        from config import get_config, validate_config
        
        # 测试配置获取
        test_config = get_config('test')
        coin_config = get_config('coin')
        
        print(f"✅ 测试配置: 默认测试数量 {test_config['default_test_count']}")
        print(f"✅ 币种配置: 支持 {len(coin_config)} 个币种")
        
        # 验证配置
        errors = validate_config()
        if errors:
            print(f"⚠️ 配置警告: {errors}")
        else:
            print("✅ 配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🧪 对敲检测测试系统 - 模块测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("币种换算", test_coin_conversions),
        ("统一交易接口", test_unified_trader),
        ("数据生成器", test_data_generator),
        ("交易记录器", test_trade_recorder),
        ("数据分析器", test_data_analyzer),
        ("配置模块", test_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有模块测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️ 部分模块测试失败，请检查相关问题。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
