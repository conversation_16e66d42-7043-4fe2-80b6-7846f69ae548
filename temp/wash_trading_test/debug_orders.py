#!/usr/bin/env python3
"""
调试订单ID问题
"""

from unified_trader import UnifiedTrader
import time

def debug_orders():
    """调试订单ID问题"""
    print("🔍 调试订单ID问题...")
    
    trader = UnifiedTrader()
    coin = "DOG"
    amount = 10.0  # 小金额测试
    
    print(f"\n📋 测试币种: {coin}")
    print(f"📋 测试金额: {amount} USDT")
    
    # 第一步：开多头（买入）
    print(f"\n📈 第一步：开多头（买入）")
    long_result = trader.buy(coin, amount)
    print(f"开多头结果: {long_result}")
    
    if not long_result.get('success'):
        print("❌ 开多头失败，停止测试")
        return
    
    long_order_id = long_result.get('order_id')
    print(f"✅ 开多头成功，订单ID: {long_order_id}")
    
    # 等待5秒
    print(f"\n⏳ 等待5秒...")
    time.sleep(5)
    
    # 第二步：开空头（卖出）
    print(f"\n📉 第二步：开空头（卖出）")
    short_result = trader.sell(coin, amount)
    print(f"开空头结果: {short_result}")
    
    if not short_result.get('success'):
        print("❌ 开空头失败，停止测试")
        return
    
    short_order_id = short_result.get('order_id')
    print(f"✅ 开空头成功，订单ID: {short_order_id}")
    
    # 等待5秒
    print(f"\n⏳ 等待5秒...")
    time.sleep(5)
    
    # 第三步：平多头（卖出）
    print(f"\n📉 第三步：平多头（卖出）")
    close_long_result = trader.sell(coin, amount)
    print(f"平多头结果: {close_long_result}")
    
    if not close_long_result.get('success'):
        print("❌ 平多头失败")
    else:
        close_long_order_id = close_long_result.get('order_id')
        print(f"✅ 平多头成功，订单ID: {close_long_order_id}")
    
    # 等待1秒
    print(f"\n⏳ 等待1秒...")
    time.sleep(1)
    
    # 第四步：平空头（买入）
    print(f"\n📈 第四步：平空头（买入）")
    close_short_result = trader.buy(coin, amount)
    print(f"平空头结果: {close_short_result}")
    
    if not close_short_result.get('success'):
        print("❌ 平空头失败")
    else:
        close_short_order_id = close_short_result.get('order_id')
        print(f"✅ 平空头成功，订单ID: {close_short_order_id}")
    
    # 总结
    print(f"\n📊 订单ID总结:")
    print(f"  开多头: {long_order_id}")
    print(f"  开空头: {short_order_id}")
    print(f"  平多头: {close_long_result.get('order_id') if close_long_result.get('success') else 'N/A'}")
    print(f"  平空头: {close_short_result.get('order_id') if close_short_result.get('success') else 'N/A'}")
    
    # 检查是否有重复
    order_ids = [
        long_order_id,
        short_order_id,
        close_long_result.get('order_id') if close_long_result.get('success') else None,
        close_short_result.get('order_id') if close_short_result.get('success') else None
    ]
    
    order_ids = [oid for oid in order_ids if oid is not None]
    unique_order_ids = set(order_ids)
    
    print(f"\n🔍 订单ID分析:")
    print(f"  总订单数: {len(order_ids)}")
    print(f"  唯一订单数: {len(unique_order_ids)}")
    
    if len(order_ids) != len(unique_order_ids):
        print("❌ 发现重复的订单ID！")
        for oid in order_ids:
            count = order_ids.count(oid)
            if count > 1:
                print(f"  重复订单ID: {oid} (出现{count}次)")
    else:
        print("✅ 所有订单ID都是唯一的")

if __name__ == "__main__":
    debug_orders()
