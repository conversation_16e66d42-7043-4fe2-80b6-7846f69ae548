#!/usr/bin/env python3
"""
显示所有币种的换算表
验证换算比例是否正确
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

from btc_trader import usdt_to_btc_orderqty, btc_orderqty_to_usdt, show_btc_conversion_table
from eth_trader import usdt_to_eth_orderqty, eth_orderqty_to_usdt, show_eth_conversion_table
from dog_trader import usdt_to_dog_orderqty, dog_orderqty_to_usdt, show_dog_conversion_table


def show_all_conversion_tables():
    """显示所有币种的换算表"""
    print("💰 对敲检测测试系统 - 币种换算表")
    print("=" * 80)
    
    # 显示修正后的换算比例
    print("📊 修正后的换算比例:")
    print("-" * 50)
    print("🐕 DOG:  1 USDT = 4 orderQty     (测试范围: 1-100 USDT)")
    print("🔷 ETH:  50 USDT = 1 orderQty    (测试范围: 50-5000 USDT)")
    print("₿ BTC:  10000 USDT = 86 orderQty (测试范围: 5000-100000 USDT)")
    print()
    
    print("⚙️ 特殊参数:")
    print("-" * 50)
    print("🐕 DOG: ppw: 'W001'")
    print("🔷 ETH: ppw: 'W001'")
    print("₿ BTC: ppw: 'W001'")
    print()
    
    # DOG换算表
    print("🐕 DOG (DOGE) 换算表")
    print("=" * 50)
    show_dog_conversion_table()
    print()
    
    # ETH换算表
    print("🔷 ETH 换算表")
    print("=" * 50)
    show_eth_conversion_table()
    print()
    
    # BTC换算表
    print("₿ BTC 换算表")
    print("=" * 50)
    show_btc_conversion_table()
    print()


def test_conversion_accuracy():
    """测试换算精度"""
    print("🧪 换算精度测试")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        # (币种, USDT金额, 期望的orderQty范围)
        ('DOG', 1, (4, 4)),
        ('DOG', 25, (100, 100)),
        ('DOG', 100, (400, 400)),
        
        ('ETH', 50, (1, 1)),
        ('ETH', 100, (2, 2)),
        ('ETH', 1000, (20, 20)),
        ('ETH', 5000, (100, 100)),
        
        ('BTC', 5000, (43, 43)),
        ('BTC', 10000, (86, 86)),
        ('BTC', 50000, (430, 430)),
        ('BTC', 100000, (860, 860))
    ]
    
    all_passed = True
    
    for coin, usdt_amount, expected_range in test_cases:
        if coin == 'DOG':
            actual_qty = usdt_to_dog_orderqty(usdt_amount)
            back_usdt = dog_orderqty_to_usdt(actual_qty)
        elif coin == 'ETH':
            actual_qty = usdt_to_eth_orderqty(usdt_amount)
            back_usdt = eth_orderqty_to_usdt(actual_qty)
        elif coin == 'BTC':
            actual_qty = usdt_to_btc_orderqty(usdt_amount)
            back_usdt = btc_orderqty_to_usdt(actual_qty)
        
        min_expected, max_expected = expected_range
        
        if min_expected <= actual_qty <= max_expected:
            status = "✅"
        else:
            status = "❌"
            all_passed = False
        
        print(f"{status} {coin}: {usdt_amount} USDT → {actual_qty} orderQty → {back_usdt:.0f} USDT "
              f"(期望: {min_expected}-{max_expected})")
    
    print(f"\n📊 测试结果: {'全部通过' if all_passed else '存在问题'}")
    return all_passed


def show_coverage_analysis():
    """显示测试范围覆盖分析"""
    print("📈 测试范围覆盖分析")
    print("=" * 50)
    
    # 分析覆盖情况
    ranges = [
        ('DOG', 1, 100, '小额交易'),
        ('ETH', 50, 5000, '中等金额'),
        ('BTC', 5000, 100000, '大额交易')
    ]
    
    total_min = min(r[1] for r in ranges)
    total_max = max(r[2] for r in ranges)
    
    print(f"🎯 目标覆盖范围: {total_min}-{total_max} USDT")
    print()
    
    for coin, min_usdt, max_usdt, description in ranges:
        coverage_start = ((min_usdt - total_min) / (total_max - total_min)) * 100
        coverage_end = ((max_usdt - total_min) / (total_max - total_min)) * 100
        coverage_width = coverage_end - coverage_start
        
        print(f"{coin}: {min_usdt}-{max_usdt} USDT ({description})")
        print(f"   覆盖范围: {coverage_start:.1f}%-{coverage_end:.1f}% (宽度: {coverage_width:.1f}%)")
        
        # 显示覆盖条
        bar_length = 50
        start_pos = int(coverage_start * bar_length / 100)
        end_pos = int(coverage_end * bar_length / 100)
        
        bar = ['-'] * bar_length
        for i in range(start_pos, min(end_pos + 1, bar_length)):
            if coin == 'DOG':
                bar[i] = '🐕'
            elif coin == 'ETH':
                bar[i] = '🔷'
            elif coin == 'BTC':
                bar[i] = '₿'
        
        print(f"   {''.join(bar)}")
        print()
    
    # 检查覆盖间隙
    gaps = []
    if ranges[1][1] > ranges[0][2]:  # ETH最小 > DOG最大
        gaps.append(f"DOG-ETH间隙: {ranges[0][2]}-{ranges[1][1]} USDT")
    if ranges[2][1] > ranges[1][2]:  # BTC最小 > ETH最大
        gaps.append(f"ETH-BTC间隙: {ranges[1][2]}-{ranges[2][1]} USDT")
    
    if gaps:
        print("⚠️ 发现覆盖间隙:")
        for gap in gaps:
            print(f"   {gap}")
    else:
        print("✅ 无覆盖间隙，完整覆盖目标范围")


def main():
    """主函数"""
    print("💰 币种换算表和覆盖分析")
    print("=" * 80)
    
    while True:
        print("\n请选择:")
        print("1. 显示所有币种换算表")
        print("2. 测试换算精度")
        print("3. 显示测试范围覆盖分析")
        print("4. 显示完整报告")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            show_all_conversion_tables()
        elif choice == '2':
            test_conversion_accuracy()
        elif choice == '3':
            show_coverage_analysis()
        elif choice == '4':
            show_all_conversion_tables()
            test_conversion_accuracy()
            print()
            show_coverage_analysis()
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
