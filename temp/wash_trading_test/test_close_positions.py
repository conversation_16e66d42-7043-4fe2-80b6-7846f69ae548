#!/usr/bin/env python3
"""
测试平仓逻辑
"""

from concurrent_execution_engine import ConcurrentExecutionEngine
from test_data_generator import WashTradingTestGenerator

def test_close_positions():
    """测试平仓逻辑"""
    print("🧪 测试平仓逻辑...")
    
    engine = ConcurrentExecutionEngine()
    generator = WashTradingTestGenerator()
    
    # 生成1个测试用例
    test_cases = generator.generate_test_batch_with_position_management(1)
    
    print(f"生成了 {len(test_cases)} 个测试用例")
    for case in test_cases:
        print(f"  {case.coin} {case.usdt_amount}U 等待{case.wait_seconds}秒")
    
    print("\n🚀 开始执行...")
    results = engine.execute_batch_concurrent(test_cases)
    
    print(f"\n📊 执行结果:")
    for i, result in enumerate(results, 1):
        print(f"  {i}. 成功: {result['success']}")
        if result['success']:
            trade_result = result['trade_result']
            print(f"     币种: {trade_result['coin']}")
            print(f"     多头订单: {trade_result.get('long_order_id')}")
            print(f"     空头订单: {trade_result.get('short_order_id')}")
            print(f"     平多头订单: {trade_result.get('close_long_order_id')}")
            print(f"     平空头订单: {trade_result.get('close_short_order_id')}")
            print(f"     执行时间: {trade_result.get('execution_time')}秒")
        else:
            print(f"     错误: {result['trade_result'].get('error')}")
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    test_close_positions()
