#!/usr/bin/env python3
"""
测试智能测试用例生成器
"""

from test_data_generator import WashTradingTestGenerator

def test_smart_generator():
    """测试智能测试用例生成器"""
    print("🧪 测试智能测试用例生成器...")
    
    generator = WashTradingTestGenerator(seed=42)  # 固定种子确保可重现
    
    print("\n📊 生成20个测试用例（带仓位管理）...")
    test_cases = generator.generate_test_batch_with_position_management(20)
    
    print(f"✅ 生成了 {len(test_cases)} 个测试用例")
    
    # 分析币种分布和间隔
    print("\n🔍 分析测试用例排列...")
    coin_positions = {'BTC': [], 'ETH': [], 'DOG': []}
    
    for i, case in enumerate(test_cases):
        coin_positions[case.coin].append(i)
        print(f"{i+1:2d}. {case.coin} {case.usdt_amount:6.1f}U {case.wait_seconds:2d}s")
    
    print("\n📈 币种位置分析:")
    for coin, positions in coin_positions.items():
        if positions:
            intervals = []
            for i in range(1, len(positions)):
                interval = positions[i] - positions[i-1]
                intervals.append(interval)
            
            avg_interval = sum(intervals) / len(intervals) if intervals else 0
            min_interval = min(intervals) if intervals else 0
            
            print(f"{coin}: 位置 {positions}")
            print(f"     间隔 {intervals}")
            print(f"     平均间隔: {avg_interval:.1f}, 最小间隔: {min_interval}")
    
    print("\n🎯 验证仓位管理规则...")
    # 检查是否有同币种连续出现的情况
    violations = []
    for i in range(1, len(test_cases)):
        if test_cases[i].coin == test_cases[i-1].coin:
            violations.append(f"位置 {i} 和 {i+1}: 连续的 {test_cases[i].coin}")
    
    if violations:
        print("❌ 发现违规情况:")
        for violation in violations:
            print(f"   {violation}")
    else:
        print("✅ 没有发现同币种连续出现的情况")
    
    print("\n📊 币种分布统计:")
    coin_counts = {'BTC': 0, 'ETH': 0, 'DOG': 0}
    for case in test_cases:
        coin_counts[case.coin] += 1
    
    total = len(test_cases)
    for coin, count in coin_counts.items():
        percentage = count / total * 100
        print(f"{coin}: {count} 个 ({percentage:.1f}%)")
    
    print("\n✅ 智能生成器测试完成！")

if __name__ == "__main__":
    test_smart_generator()
