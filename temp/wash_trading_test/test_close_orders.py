#!/usr/bin/env python3
"""
测试新的平仓方法
"""

from unified_trader import UnifiedTrader
import time

def test_close_orders():
    """测试新的平仓方法"""
    print("🧪 测试新的平仓方法...")
    
    trader = UnifiedTrader()
    coin = "DOG"
    amount = 5.0  # 小金额测试
    
    print(f"\n📋 测试币种: {coin}")
    print(f"📋 测试金额: {amount} USDT")
    
    # 第一步：开多头（买入）
    print(f"\n📈 第一步：开多头（买入）")
    long_result = trader.buy(coin, amount)
    print(f"开多头结果: {long_result}")
    
    if not long_result.get('success'):
        print("❌ 开多头失败，停止测试")
        return
    
    long_order_id = long_result.get('order_id')
    print(f"✅ 开多头成功，订单ID: {long_order_id}")
    
    # 等待3秒
    print(f"\n⏳ 等待3秒...")
    time.sleep(3)
    
    # 第二步：开空头（卖出）
    print(f"\n📉 第二步：开空头（卖出）")
    short_result = trader.sell(coin, amount)
    print(f"开空头结果: {short_result}")
    
    if not short_result.get('success'):
        print("❌ 开空头失败，停止测试")
        return
    
    short_order_id = short_result.get('order_id')
    print(f"✅ 开空头成功，订单ID: {short_order_id}")
    
    # 等待3秒
    print(f"\n⏳ 等待3秒...")
    time.sleep(3)
    
    # 第三步：平多头（使用新方法）
    print(f"\n📉 第三步：平多头（使用新的close_long方法）")
    close_long_result = trader.close_long(coin, amount)
    print(f"平多头结果: {close_long_result}")
    
    if close_long_result.get('success'):
        close_long_order_id = close_long_result.get('order_id')
        print(f"✅ 平多头成功，订单ID: {close_long_order_id}")
    else:
        print(f"❌ 平多头失败: {close_long_result.get('message')}")
    
    # 等待1秒
    print(f"\n⏳ 等待1秒...")
    time.sleep(1)
    
    # 第四步：平空头（使用新方法）
    print(f"\n📈 第四步：平空头（使用新的close_short方法）")
    close_short_result = trader.close_short(coin, amount)
    print(f"平空头结果: {close_short_result}")
    
    if close_short_result.get('success'):
        close_short_order_id = close_short_result.get('order_id')
        print(f"✅ 平空头成功，订单ID: {close_short_order_id}")
    else:
        print(f"❌ 平空头失败: {close_short_result.get('message')}")
    
    # 总结
    print(f"\n📊 订单ID总结:")
    print(f"  开多头: {long_order_id}")
    print(f"  开空头: {short_order_id}")
    print(f"  平多头: {close_long_result.get('order_id') if close_long_result.get('success') else 'N/A'}")
    print(f"  平空头: {close_short_result.get('order_id') if close_short_result.get('success') else 'N/A'}")
    
    # 检查参数
    print(f"\n🔍 平仓参数验证:")
    if close_long_result.get('success'):
        print(f"  平多头参数: type=5, reduceOnly=1, side=1")
    if close_short_result.get('success'):
        print(f"  平空头参数: type=5, reduceOnly=1, side=2")

if __name__ == "__main__":
    test_close_orders()
