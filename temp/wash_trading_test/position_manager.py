#!/usr/bin/env python3
"""
仓位管理器
确保每个币种在同一时间只有一组完整的多空交易，避免仓位叠加
"""

import time
import threading
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class PositionStatus(Enum):
    """仓位状态"""
    IDLE = "idle"                    # 空闲，可以开新仓
    LONG_OPENING = "long_opening"    # 多头开仓中
    LONG_HOLDING = "long_holding"    # 多头持仓中
    LONG_CLOSING = "long_closing"    # 多头平仓中
    SHORT_OPENING = "short_opening"  # 空头开仓中
    SHORT_HOLDING = "short_holding"  # 空头持仓中
    SHORT_CLOSING = "short_closing"  # 空头平仓中
    BOTH_HOLDING = "both_holding"    # 多空都持仓中（对敲状态）
    CLOSING_ALL = "closing_all"      # 全部平仓中


@dataclass
class PositionInfo:
    """仓位信息"""
    coin: str
    status: PositionStatus
    long_order_id: Optional[str] = None
    short_order_id: Optional[str] = None
    long_amount: float = 0.0
    short_amount: float = 0.0
    start_time: Optional[datetime] = None
    last_update: Optional[datetime] = None
    group_id: Optional[str] = None  # 交易组ID


class PositionManager:
    """仓位管理器"""
    
    def __init__(self):
        """初始化仓位管理器"""
        self.positions: Dict[str, PositionInfo] = {}
        self.lock = threading.RLock()
        self.supported_coins = ['BTC', 'ETH', 'DOG']
        
        # 初始化所有币种的仓位状态
        for coin in self.supported_coins:
            self.positions[coin] = PositionInfo(
                coin=coin,
                status=PositionStatus.IDLE,
                last_update=datetime.now()
            )
    
    def can_start_new_group(self, coin: str) -> bool:
        """检查是否可以开始新的交易组
        
        Args:
            coin: 币种
            
        Returns:
            是否可以开始新交易组
        """
        with self.lock:
            if coin not in self.positions:
                return False
            
            position = self.positions[coin]
            return position.status == PositionStatus.IDLE
    
    def start_new_group(self, coin: str, group_id: str) -> bool:
        """开始新的交易组
        
        Args:
            coin: 币种
            group_id: 交易组ID
            
        Returns:
            是否成功开始
        """
        with self.lock:
            if not self.can_start_new_group(coin):
                return False
            
            position = self.positions[coin]
            position.status = PositionStatus.IDLE
            position.group_id = group_id
            position.start_time = datetime.now()
            position.last_update = datetime.now()
            position.long_order_id = None
            position.short_order_id = None
            position.long_amount = 0.0
            position.short_amount = 0.0
            
            return True
    
    def open_long_position(self, coin: str, order_id: str, amount: float) -> bool:
        """开多头仓位
        
        Args:
            coin: 币种
            order_id: 订单ID
            amount: 交易金额
            
        Returns:
            是否成功
        """
        with self.lock:
            if coin not in self.positions:
                return False
            
            position = self.positions[coin]
            
            # 只有在空闲或空头持仓状态下才能开多头
            if position.status not in [PositionStatus.IDLE, PositionStatus.SHORT_HOLDING]:
                return False
            
            position.long_order_id = order_id
            position.long_amount = amount
            position.last_update = datetime.now()
            
            if position.status == PositionStatus.IDLE:
                position.status = PositionStatus.LONG_HOLDING
            elif position.status == PositionStatus.SHORT_HOLDING:
                position.status = PositionStatus.BOTH_HOLDING
            
            return True
    
    def open_short_position(self, coin: str, order_id: str, amount: float) -> bool:
        """开空头仓位
        
        Args:
            coin: 币种
            order_id: 订单ID
            amount: 交易金额
            
        Returns:
            是否成功
        """
        with self.lock:
            if coin not in self.positions:
                return False
            
            position = self.positions[coin]
            
            # 只有在空闲或多头持仓状态下才能开空头
            if position.status not in [PositionStatus.IDLE, PositionStatus.LONG_HOLDING]:
                return False
            
            position.short_order_id = order_id
            position.short_amount = amount
            position.last_update = datetime.now()
            
            if position.status == PositionStatus.IDLE:
                position.status = PositionStatus.SHORT_HOLDING
            elif position.status == PositionStatus.LONG_HOLDING:
                position.status = PositionStatus.BOTH_HOLDING
            
            return True
    
    def close_long_position(self, coin: str) -> bool:
        """平多头仓位
        
        Args:
            coin: 币种
            
        Returns:
            是否成功
        """
        with self.lock:
            if coin not in self.positions:
                return False
            
            position = self.positions[coin]
            
            # 只有在多头持仓或多空都持仓状态下才能平多头
            if position.status not in [PositionStatus.LONG_HOLDING, PositionStatus.BOTH_HOLDING]:
                return False
            
            position.long_order_id = None
            position.long_amount = 0.0
            position.last_update = datetime.now()
            
            if position.status == PositionStatus.LONG_HOLDING:
                position.status = PositionStatus.IDLE
            elif position.status == PositionStatus.BOTH_HOLDING:
                position.status = PositionStatus.SHORT_HOLDING
            
            return True
    
    def close_short_position(self, coin: str) -> bool:
        """平空头仓位
        
        Args:
            coin: 币种
            
        Returns:
            是否成功
        """
        with self.lock:
            if coin not in self.positions:
                return False
            
            position = self.positions[coin]
            
            # 只有在空头持仓或多空都持仓状态下才能平空头
            if position.status not in [PositionStatus.SHORT_HOLDING, PositionStatus.BOTH_HOLDING]:
                return False
            
            position.short_order_id = None
            position.short_amount = 0.0
            position.last_update = datetime.now()
            
            if position.status == PositionStatus.SHORT_HOLDING:
                position.status = PositionStatus.IDLE
            elif position.status == PositionStatus.BOTH_HOLDING:
                position.status = PositionStatus.LONG_HOLDING
            
            return True
    
    def close_all_positions(self, coin: str) -> bool:
        """平所有仓位
        
        Args:
            coin: 币种
            
        Returns:
            是否成功
        """
        with self.lock:
            if coin not in self.positions:
                return False
            
            position = self.positions[coin]
            position.status = PositionStatus.IDLE
            position.long_order_id = None
            position.short_order_id = None
            position.long_amount = 0.0
            position.short_amount = 0.0
            position.group_id = None
            position.last_update = datetime.now()
            
            return True
    
    def get_position_status(self, coin: str) -> Optional[PositionInfo]:
        """获取仓位状态
        
        Args:
            coin: 币种
            
        Returns:
            仓位信息
        """
        with self.lock:
            return self.positions.get(coin)
    
    def get_all_positions(self) -> Dict[str, PositionInfo]:
        """获取所有仓位状态"""
        with self.lock:
            return self.positions.copy()
    
    def get_busy_coins(self) -> Set[str]:
        """获取正在交易中的币种"""
        with self.lock:
            busy_coins = set()
            for coin, position in self.positions.items():
                if position.status != PositionStatus.IDLE:
                    busy_coins.add(coin)
            return busy_coins
    
    def get_available_coins(self) -> Set[str]:
        """获取可用于新交易的币种"""
        with self.lock:
            available_coins = set()
            for coin, position in self.positions.items():
                if position.status == PositionStatus.IDLE:
                    available_coins.add(coin)
            return available_coins
    
    def print_status(self):
        """打印仓位状态"""
        with self.lock:
            print("\n📊 仓位管理器状态")
            print("=" * 50)
            
            for coin, position in self.positions.items():
                status_emoji = {
                    PositionStatus.IDLE: "🟢",
                    PositionStatus.LONG_HOLDING: "🔵",
                    PositionStatus.SHORT_HOLDING: "🔴",
                    PositionStatus.BOTH_HOLDING: "🟡"
                }.get(position.status, "⚪")
                
                print(f"{status_emoji} {coin}: {position.status.value}")
                if position.group_id:
                    print(f"   交易组: {position.group_id}")
                if position.long_amount > 0:
                    print(f"   多头: {position.long_amount} USDT (订单: {position.long_order_id})")
                if position.short_amount > 0:
                    print(f"   空头: {position.short_amount} USDT (订单: {position.short_order_id})")
                if position.start_time:
                    duration = datetime.now() - position.start_time
                    print(f"   持续时间: {duration.total_seconds():.1f}秒")
                print()


# 全局仓位管理器实例
position_manager = PositionManager()


if __name__ == "__main__":
    # 测试仓位管理器
    pm = PositionManager()
    
    print("测试仓位管理器...")
    pm.print_status()
    
    # 测试开仓
    print("开始BTC交易组...")
    pm.start_new_group('BTC', 'group_001')
    pm.open_long_position('BTC', 'order_001', 10000.0)
    pm.print_status()
    
    print("开空头仓位...")
    pm.open_short_position('BTC', 'order_002', 10000.0)
    pm.print_status()
    
    print("平多头仓位...")
    pm.close_long_position('BTC')
    pm.print_status()
    
    print("平空头仓位...")
    pm.close_short_position('BTC')
    pm.print_status()
