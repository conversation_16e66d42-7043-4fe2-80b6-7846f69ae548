#!/usr/bin/env python3
"""
测试导出数据的完整性和正确性
"""

import json
import csv
import os
from datetime import datetime


def test_csv_data(csv_file):
    """测试CSV数据"""
    print(f"🔍 测试CSV文件: {csv_file}")
    
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return False
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            records = list(reader)
        
        print(f"✅ CSV文件读取成功")
        print(f"📊 记录数量: {len(records)}")
        
        # 检查必要字段
        required_fields = ['record_id', 'coin', 'usdt_amount', 'trade_success', 'created_at']
        if records:
            first_record = records[0]
            missing_fields = [field for field in required_fields if field not in first_record]
            if missing_fields:
                print(f"❌ 缺少必要字段: {missing_fields}")
                return False
            else:
                print(f"✅ 包含所有必要字段")
        
        # 统计信息
        if records:
            coins = {}
            success_count = 0
            for record in records:
                coin = record.get('coin', 'Unknown')
                coins[coin] = coins.get(coin, 0) + 1
                if record.get('trade_success') == '1':
                    success_count += 1
            
            print(f"📈 币种分布: {coins}")
            print(f"📈 成功率: {success_count}/{len(records)} ({success_count/len(records)*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV文件测试失败: {e}")
        return False


def test_json_data(json_file):
    """测试JSON数据"""
    print(f"\n🔍 测试JSON文件: {json_file}")
    
    if not os.path.exists(json_file):
        print(f"❌ 文件不存在: {json_file}")
        return False
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ JSON文件读取成功")
        
        # 检查结构
        if 'metadata' in data:
            metadata = data['metadata']
            print(f"📊 元数据: {metadata.get('total_records', 'N/A')} 条记录")
            print(f"📅 导出时间: {metadata.get('exported_at', 'N/A')}")
        
        if 'records' in data:
            records = data['records']
            print(f"📊 记录数量: {len(records)}")
            
            # 统计信息
            if records:
                coins = {}
                success_count = 0
                for record in records:
                    coin = record.get('coin', 'Unknown')
                    coins[coin] = coins.get(coin, 0) + 1
                    if record.get('trade_success'):
                        success_count += 1
                
                print(f"📈 币种分布: {coins}")
                print(f"📈 成功率: {success_count}/{len(records)} ({success_count/len(records)*100:.1f}%)")
        
        if 'statistics' in data:
            stats = data['statistics']
            print(f"📊 统计信息: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON文件测试失败: {e}")
        return False


def test_analysis_data(json_file):
    """测试分析结果数据"""
    print(f"\n🔍 测试分析结果文件: {json_file}")
    
    if not os.path.exists(json_file):
        print(f"❌ 文件不存在: {json_file}")
        return False
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 分析结果文件读取成功")
        
        # 检查分析元数据
        if 'analysis_metadata' in data:
            metadata = data['analysis_metadata']
            print(f"📊 分析元数据:")
            print(f"   - 分析时间: {metadata.get('generated_at', 'N/A')}")
            print(f"   - 分析交易对数: {metadata.get('total_pairs_analyzed', 'N/A')}")
            print(f"   - 发现对敲交易对: {metadata.get('wash_trading_pairs_found', 'N/A')}")
            print(f"   - 对敲率: {metadata.get('wash_trading_rate', 'N/A')}")
        
        # 检查风险分布
        if 'risk_distribution' in data:
            risk_dist = data['risk_distribution']
            print(f"📊 风险分布: {risk_dist}")
        
        # 检查币种分布
        if 'coin_distribution' in data:
            coin_dist = data['coin_distribution']
            print(f"📊 币种分布: {coin_dist}")
        
        # 检查评分统计
        if 'score_statistics' in data:
            score_stats = data['score_statistics']
            print(f"📊 评分统计:")
            for score_type, stats in score_stats.items():
                if isinstance(stats, dict):
                    print(f"   - {score_type}: min={stats.get('min', 'N/A'):.3f}, "
                          f"max={stats.get('max', 'N/A'):.3f}, avg={stats.get('avg', 'N/A'):.3f}")
        
        # 检查顶级对敲交易对
        if 'top_wash_trading_pairs' in data:
            top_pairs = data['top_wash_trading_pairs']
            print(f"📊 顶级对敲交易对数量: {len(top_pairs)}")
            for i, pair in enumerate(top_pairs[:3], 1):
                print(f"   {i}. {pair.get('coin', 'N/A')} - 评分: {pair.get('wash_score', 'N/A'):.3f}, "
                      f"风险: {pair.get('risk_level', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析结果文件测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🧪 对敲测试数据导出验证")
    print("=" * 50)
    
    # 查找最新的导出文件
    files = os.listdir('.')
    trade_csv_files = [f for f in files if f.startswith('all_trade_records_') and f.endswith('.csv')]
    trade_json_files = [f for f in files if f.startswith('all_trade_records_') and f.endswith('.json')]
    analysis_json_files = [f for f in files if f.startswith('all_wash_trading_analysis_') and f.endswith('.json')]
    
    if not trade_csv_files:
        print("❌ 没有找到交易记录CSV文件")
        return
    
    if not trade_json_files:
        print("❌ 没有找到交易记录JSON文件")
        return
    
    # 使用最新的文件
    latest_csv = sorted(trade_csv_files)[-1]
    latest_json = sorted(trade_json_files)[-1]
    
    # 测试交易记录
    csv_ok = test_csv_data(latest_csv)
    json_ok = test_json_data(latest_json)
    
    # 测试分析结果
    analysis_ok = True
    if analysis_json_files:
        latest_analysis = sorted(analysis_json_files)[-1]
        analysis_ok = test_analysis_data(latest_analysis)
    else:
        print("\n⚠️  没有找到分析结果文件")
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"✅ CSV文件: {'通过' if csv_ok else '失败'}")
    print(f"✅ JSON文件: {'通过' if json_ok else '失败'}")
    print(f"✅ 分析结果: {'通过' if analysis_ok else '失败'}")
    
    if csv_ok and json_ok and analysis_ok:
        print("\n🎉 所有测试通过！数据导出完整且正确。")
    else:
        print("\n❌ 部分测试失败，请检查数据文件。")


if __name__ == "__main__":
    main()
