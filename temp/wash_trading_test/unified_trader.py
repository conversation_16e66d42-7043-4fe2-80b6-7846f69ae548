#!/usr/bin/env python3
"""
统一交易接口
支持BTC、ETH、DOG三个币种的统一交易操作
"""

import sys
import os
import time
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient

# 导入各币种的交易模块
from btc_trader import btc_trade_sequence, usdt_to_btc_orderqty, btc_orderqty_to_usdt
from eth_trader import eth_trade_sequence, usdt_to_eth_orderqty, eth_orderqty_to_usdt
from dog_trader import dog_trade_sequence, usdt_to_dog_orderqty, dog_orderqty_to_usdt


@dataclass
class CoinConfig:
    """币种配置"""
    symbol: str          # 交易对符号
    min_usdt: float      # 最小交易金额(USDT)
    max_usdt: float      # 最大交易金额(USDT)
    usdt_to_orderqty_ratio: float  # USDT到orderQty的换算比例
    name: str            # 币种名称
    emoji: str           # 币种表情符号


class UnifiedTrader:
    """统一交易器"""

    # 类级别的日志初始化标志
    _logger_initialized = False

    # 币种配置
    COIN_CONFIGS = {
        'DOG': CoinConfig(
            symbol='doge-usdt',
            min_usdt=1.0,
            max_usdt=100.0,
            usdt_to_orderqty_ratio=4.0,  # 1 USDT = 4 orderQty
            name='DOGE',
            emoji='🐕'
        ),
        'ETH': CoinConfig(
            symbol='eth-usdt',
            min_usdt=50.0,
            max_usdt=5000.0,
            usdt_to_orderqty_ratio=1.0/50.0,  # 50 USDT = 1 orderQty
            name='ETH',
            emoji='🔷'
        ),
        'BTC': CoinConfig(
            symbol='btc-usdt',
            min_usdt=5000.0,
            max_usdt=100000.0,
            usdt_to_orderqty_ratio=86.0/10000.0,  # 10000 USDT = 86 orderQty
            name='BTC',
            emoji='₿'
        )
    }
    
    def __init__(self):
        """初始化统一交易器"""
        # 计算正确的config.yaml路径
        current_dir = os.path.dirname(os.path.abspath(__file__))  # temp/wash_trading_test
        project_root = os.path.dirname(os.path.dirname(current_dir))  # web
        config_path = os.path.join(project_root, 'trading_bot', 'config.yaml')

        self.config = load_config(config_path)

        # 只在第一次初始化日志系统
        if not UnifiedTrader._logger_initialized:
            setup_logger(self.config)
            UnifiedTrader._logger_initialized = True

        self.logger = get_logger('UnifiedTrader')
        self.client = BipcClient(self.config)
    
    def usdt_to_orderqty(self, coin: str, usdt_amount: float) -> int:
        """USDT金额转换为orderQty - 使用各币种专用函数"""
        if coin == 'BTC':
            return usdt_to_btc_orderqty(usdt_amount)
        elif coin == 'ETH':
            return usdt_to_eth_orderqty(usdt_amount)
        elif coin == 'DOG':
            return usdt_to_dog_orderqty(usdt_amount)
        else:
            raise ValueError(f"不支持的币种: {coin}")

    def orderqty_to_usdt(self, coin: str, orderqty: int) -> float:
        """orderQty转换为USDT金额 - 使用各币种专用函数"""
        if coin == 'BTC':
            return btc_orderqty_to_usdt(orderqty)
        elif coin == 'ETH':
            return eth_orderqty_to_usdt(orderqty)
        elif coin == 'DOG':
            return dog_orderqty_to_usdt(orderqty)
        else:
            raise ValueError(f"不支持的币种: {coin}")
    
    def validate_amount(self, coin: str, usdt_amount: float) -> bool:
        """验证交易金额是否在允许范围内"""
        if coin not in self.COIN_CONFIGS:
            return False
        
        config = self.COIN_CONFIGS[coin]
        return config.min_usdt <= usdt_amount <= config.max_usdt
    
    def place_order(self, coin: str, usdt_amount: float, side: int, order_type: str = "2") -> Dict[str, Any]:
        """下单
        
        Args:
            coin: 币种 ('BTC', 'ETH', 'DOG')
            usdt_amount: USDT交易金额
            side: 交易方向 (1=卖出, 2=买入)
            order_type: 订单类型 ("2"=市价单)
            
        Returns:
            下单结果
        """
        if coin not in self.COIN_CONFIGS:
            return {'success': False, 'error': 'unsupported_coin', 'message': f'不支持的币种: {coin}'}
        
        if not self.validate_amount(coin, usdt_amount):
            config = self.COIN_CONFIGS[coin]
            return {
                'success': False, 
                'error': 'invalid_amount', 
                'message': f'{coin}交易金额必须在{config.min_usdt}-{config.max_usdt} USDT之间'
            }
        
        try:
            config = self.COIN_CONFIGS[coin]
            orderqty = self.usdt_to_orderqty(coin, usdt_amount)
            
            order_data = {
                "symbol": config.symbol,
                "orderQty": orderqty,
                "side": side,
                "type": order_type,
                "source": 1
            }
            
            self.logger.info(f"{coin}开仓下单: {order_data}")

            url = self.client.endpoints.place_order
            result = self.client._make_request('POST', url, data=order_data, auth_required=True)

            self.logger.info(f"{coin}开仓API响应: {result}")
            
            if result.get('code') == 200:
                order_id = None
                if 'data' in result and isinstance(result['data'], dict):
                    order_id = result['data'].get('orderId')
                
                return {
                    'success': True,
                    'order_id': order_id,
                    'coin': coin,
                    'usdt_amount': usdt_amount,
                    'orderqty': orderqty,
                    'side': side,
                    'timestamp': datetime.now().isoformat(),
                    'raw_result': result
                }
            else:
                return {
                    'success': False,
                    'error': 'api_error',
                    'message': result.get('message', '未知错误'),
                    'raw_result': result
                }
                
        except Exception as e:
            self.logger.error(f"{coin}下单异常: {e}")
            return {
                'success': False,
                'error': 'exception',
                'message': str(e)
            }
    
    def buy(self, coin: str, usdt_amount: float) -> Dict[str, Any]:
        """买入"""
        return self.place_order(coin, usdt_amount, side=2)
    
    def sell(self, coin: str, usdt_amount: float) -> Dict[str, Any]:
        """卖出"""
        return self.place_order(coin, usdt_amount, side=1)

    def close_long(self, coin: str, usdt_amount: float) -> Dict[str, Any]:
        """平多头仓位（使用平仓专用参数）"""
        return self.place_close_order(coin, usdt_amount, side=1)

    def close_short(self, coin: str, usdt_amount: float) -> Dict[str, Any]:
        """平空头仓位（使用平仓专用参数）"""
        return self.place_close_order(coin, usdt_amount, side=2)

    def place_close_order(self, coin: str, usdt_amount: float, side: int) -> Dict[str, Any]:
        """下平仓单（使用真实的平仓参数）

        Args:
            coin: 币种 ('BTC', 'ETH', 'DOG')
            usdt_amount: USDT交易金额
            side: 交易方向 (1=平多头, 2=平空头)

        Returns:
            下单结果
        """
        if coin not in self.COIN_CONFIGS:
            return {'success': False, 'error': 'unsupported_coin', 'message': f'不支持的币种: {coin}'}

        if not self.validate_amount(coin, usdt_amount):
            config = self.COIN_CONFIGS[coin]
            return {
                'success': False,
                'error': 'invalid_amount',
                'message': f'{coin}交易金额必须在{config.min_usdt}-{config.max_usdt} USDT之间'
            }

        try:
            config = self.COIN_CONFIGS[coin]
            orderqty = self.usdt_to_orderqty(coin, usdt_amount)

            # 使用真实的平仓参数
            order_data = {
                "symbol": config.symbol,
                "orderQty": orderqty,
                "side": side,
                "type": 5,          # 平仓单类型（根据你的发现）
                "source": 1,
                "reduceOnly": 1     # 平仓专用参数
            }

            self.logger.info(f"{coin}平仓下单: {order_data}")

            url = self.client.endpoints.place_order
            result = self.client._make_request('POST', url, data=order_data, auth_required=True)

            if result.get('code') == 200:
                order_id = None
                if 'data' in result and isinstance(result['data'], dict):
                    order_id = result['data'].get('orderId')

                return {
                    'success': True,
                    'order_id': order_id,
                    'coin': coin,
                    'usdt_amount': usdt_amount,
                    'orderqty': orderqty,
                    'side': side,
                    'order_type': 'close',
                    'timestamp': datetime.now().isoformat(),
                    'raw_result': result
                }
            else:
                return {
                    'success': False,
                    'error': 'api_error',
                    'message': result.get('message', '未知错误'),
                    'raw_result': result
                }

        except Exception as e:
            self.logger.error(f"{coin}平仓下单异常: {e}")
            return {
                'success': False,
                'error': 'exception',
                'message': str(e)
            }

    def trade_sequence(self, coin: str, usdt_amount: float, wait_seconds: int = 10) -> Dict[str, Any]:
        """完整交易序列：买入→等待→卖出 - 使用各币种专用函数

        Args:
            coin: 币种
            usdt_amount: 交易金额
            wait_seconds: 等待时间

        Returns:
            交易结果
        """
        if coin not in self.COIN_CONFIGS:
            return {'success': False, 'error': 'unsupported_coin'}

        start_time = datetime.now()
        self.logger.info(f"开始{coin}交易序列: {usdt_amount} USDT, 等待{wait_seconds}秒")

        try:
            # 使用各币种专用的交易函数
            if coin == 'BTC':
                result = btc_trade_sequence(usdt_amount, wait_seconds)
            elif coin == 'ETH':
                result = eth_trade_sequence(usdt_amount, wait_seconds)
            elif coin == 'DOG':
                result = dog_trade_sequence(usdt_amount, wait_seconds)
            else:
                return {'success': False, 'error': 'unsupported_coin'}

            end_time = datetime.now()

            if result and result.get('success'):
                # 标准化返回格式
                return {
                    'success': True,
                    'coin': coin,
                    'usdt_amount': usdt_amount,
                    'orderqty': result.get('orderQty', 0),
                    'buy_order_id': result.get('buy_order_id', ''),
                    'sell_order_id': result.get('sell_order_id', ''),
                    'start_time': start_time.isoformat(),
                    'buy_time': start_time.isoformat(),  # 近似值
                    'sell_time': end_time.isoformat(),   # 近似值
                    'end_time': end_time.isoformat(),
                    'planned_wait_seconds': wait_seconds,
                    'actual_wait_seconds': result.get('wait_seconds', wait_seconds),
                    'total_duration_seconds': (end_time - start_time).total_seconds(),
                    'buy_result': {'success': True, 'order_id': result.get('buy_order_id', '')},
                    'sell_result': {'success': True, 'order_id': result.get('sell_order_id', '')}
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'unknown') if result else 'no_result',
                    'message': result.get('message', '交易失败') if result else '无返回结果',
                    'coin': coin,
                    'usdt_amount': usdt_amount
                }

        except Exception as e:
            self.logger.error(f"{coin}交易序列异常: {e}")
            return {
                'success': False,
                'error': 'exception',
                'message': str(e),
                'coin': coin,
                'usdt_amount': usdt_amount
            }
    
    def get_coin_info(self, coin: str) -> Optional[CoinConfig]:
        """获取币种信息"""
        return self.COIN_CONFIGS.get(coin)
    
    def get_supported_coins(self) -> list:
        """获取支持的币种列表"""
        return list(self.COIN_CONFIGS.keys())
    
    def show_coin_ranges(self):
        """显示各币种的交易范围"""
        print("📊 支持的币种和交易范围")
        print("=" * 50)
        
        for coin, config in self.COIN_CONFIGS.items():
            print(f"{config.emoji} {config.name} ({config.symbol})")
            print(f"   交易范围: {config.min_usdt}-{config.max_usdt} USDT")
            print(f"   换算比例: 1 USDT = {config.usdt_to_orderqty_ratio} orderQty")
            print()

    def get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额和盈亏信息"""
        try:
            url = f"{self.client.base_url}/swap/private/future/account/list/balance?all=true"
            result = self.client._make_request('GET', url, auth_required=True)

            if result.get('code') == 200 and 'data' in result:
                data = result['data'][0] if result['data'] else {}
                accounts = data.get('accounts', {})
                usdt_account = accounts.get('USDT', {})

                return {
                    'success': True,
                    'equity': usdt_account.get('equity', 0),
                    'available_balance': usdt_account.get('availableBalance', 0),
                    'position_margin': usdt_account.get('positionMargin', 0),
                    'unrealised_pnl': usdt_account.get('unrealisedPNL', 0),
                    'locked': usdt_account.get('locked', 0),
                    'raw_data': usdt_account
                }
            else:
                return {
                    'success': False,
                    'error': 'api_error',
                    'message': f"API返回错误: {result.get('message', 'Unknown error')}"
                }

        except Exception as e:
            self.logger.error(f"获取账户余额失败: {e}")
            return {
                'success': False,
                'error': 'exception',
                'message': str(e)
            }


def main():
    """主函数 - 测试统一交易接口"""
    trader = UnifiedTrader()
    
    print("🔄 统一交易接口测试")
    print("=" * 40)
    
    trader.show_coin_ranges()
    
    while True:
        print("\n请选择测试:")
        print("1. DOG交易测试 (1-100 USDT)")
        print("2. ETH交易测试 (50-5000 USDT)")
        print("3. BTC交易测试 (5000-100000 USDT)")
        print("4. 自定义交易测试")
        print("5. 显示币种信息")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            try:
                amount = float(input("请输入DOG交易金额 (1-100 USDT): "))
                wait_time = int(input("请输入等待时间 (1-120秒): "))
                result = trader.trade_sequence('DOG', amount, wait_time)
                print(f"交易结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '2':
            try:
                amount = float(input("请输入ETH交易金额 (50-5000 USDT): "))
                wait_time = int(input("请输入等待时间 (1-120秒): "))
                result = trader.trade_sequence('ETH', amount, wait_time)
                print(f"交易结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '3':
            try:
                amount = float(input("请输入BTC交易金额 (5000-100000 USDT): "))
                wait_time = int(input("请输入等待时间 (1-120秒): "))
                result = trader.trade_sequence('BTC', amount, wait_time)
                print(f"交易结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '4':
            try:
                coin = input("请输入币种 (DOG/ETH/BTC): ").upper()
                amount = float(input("请输入交易金额 (USDT): "))
                wait_time = int(input("请输入等待时间 (1-120秒): "))
                result = trader.trade_sequence(coin, amount, wait_time)
                print(f"交易结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '5':
            trader.show_coin_ranges()
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
