#!/usr/bin/env python3
"""
对敲检测测试执行引擎
自动执行5000组测试数据，三个币种同时进行
"""

import sys
import os
import time
import json
import threading
import queue
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

from test_data_generator import WashTradingTestGenerator, TestCase
from unified_trader import UnifiedTrader
from trade_recorder import TradeRecorder
from position_manager import PositionManager, position_manager
from dataclasses import dataclass


@dataclass
class TradeTask:
    """交易任务"""
    test_case: TestCase
    group_id: str
    open_time: datetime
    close_time: datetime
    status: str = "pending"  # pending, opened, closed, failed
    buy_order_id: Optional[str] = None
    sell_order_id: Optional[str] = None
    buy_result: Optional[Dict[str, Any]] = None
    sell_result: Optional[Dict[str, Any]] = None


class TestExecutionEngine:
    """测试执行引擎"""
    
    def __init__(self, max_workers: int = 3, delay_between_tests: float = 1.0):
        """初始化执行引擎

        Args:
            max_workers: 最大并发线程数
            delay_between_tests: 测试之间的延迟时间（秒）
        """
        self.max_workers = max_workers
        self.delay_between_tests = delay_between_tests
        self.position_manager = position_manager
        
        # 初始化组件
        self.generator = WashTradingTestGenerator()
        self.trader = UnifiedTrader()
        self.recorder = TradeRecorder()
        
        # 执行状态
        self.is_running = False
        self.current_test = 0
        self.total_tests = 0
        self.start_time = None
        self.results_queue = queue.Queue()
        
        # 统计信息
        self.execution_stats = {
            'total_tests': 0,
            'completed_tests': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'coin_stats': {'DOG': 0, 'ETH': 0, 'BTC': 0},
            'errors': []
        }
    
    def execute_single_test(self, test_case: TestCase) -> Dict[str, Any]:
        """执行单个测试用例
        
        Args:
            test_case: 测试用例
            
        Returns:
            执行结果
        """
        try:
            print(f"🔄 执行测试 #{test_case.case_id}: {test_case.description}")
            
            # 执行交易序列
            trade_result = self.trader.trade_sequence(
                coin=test_case.coin,
                usdt_amount=test_case.usdt_amount,
                wait_seconds=test_case.wait_seconds
            )
            
            # 记录交易数据
            trade_record = self.recorder.record_trade(
                trade_result=trade_result,
                test_case_id=test_case.case_id,
                test_type=test_case.test_type
            )
            
            # 更新统计
            self.execution_stats['completed_tests'] += 1
            if trade_result.get('success'):
                self.execution_stats['successful_tests'] += 1
                print(f"✅ 测试 #{test_case.case_id} 成功完成")
            else:
                self.execution_stats['failed_tests'] += 1
                error_msg = trade_result.get('message', '未知错误')
                self.execution_stats['errors'].append({
                    'test_id': test_case.case_id,
                    'error': error_msg,
                    'timestamp': datetime.now().isoformat()
                })
                print(f"❌ 测试 #{test_case.case_id} 失败: {error_msg}")
            
            self.execution_stats['coin_stats'][test_case.coin] += 1
            
            return {
                'test_case': test_case,
                'trade_result': trade_result,
                'trade_record': trade_record,
                'success': trade_result.get('success', False)
            }
            
        except Exception as e:
            error_msg = f"测试执行异常: {str(e)}"
            self.execution_stats['failed_tests'] += 1
            self.execution_stats['errors'].append({
                'test_id': test_case.case_id,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            })
            print(f"❌ 测试 #{test_case.case_id} 异常: {error_msg}")
            
            return {
                'test_case': test_case,
                'trade_result': {'success': False, 'error': 'exception', 'message': error_msg},
                'trade_record': None,
                'success': False
            }

    def execute_single_test_with_position_management(self, test_case: TestCase) -> Dict[str, Any]:
        """执行单个测试用例（带仓位管理）

        Args:
            test_case: 测试用例

        Returns:
            执行结果
        """
        coin = test_case.coin
        group_id = f"{coin}_{test_case.case_id}_{datetime.now().strftime('%H%M%S')}"

        print(f"🔄 执行测试 #{test_case.case_id}: {test_case.description}")

        # 开始新的交易组
        if not self.position_manager.start_new_group(coin, group_id):
            return {
                'test_case': test_case,
                'execution_time': 0,
                'trade_result': {'success': False, 'error': 'cannot_start_group'},
                'trade_record': None,
                'success': False
            }

        try:
            # 执行交易
            result = self.execute_single_test(test_case)

            # 确保交易完成后清理仓位
            self.position_manager.close_all_positions(coin)

            return result

        except Exception as e:
            # 异常情况下也要清理仓位
            self.position_manager.close_all_positions(coin)
            error_msg = str(e)
            print(f"❌ 测试执行异常: {error_msg}")

            return {
                'test_case': test_case,
                'execution_time': 0,
                'trade_result': {'success': False, 'error': 'exception', 'message': error_msg},
                'trade_record': None,
                'success': False
            }

    def execute_batch_sequential(self, test_cases: List[TestCase],
                                progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """顺序执行测试批次
        
        Args:
            test_cases: 测试用例列表
            progress_callback: 进度回调函数
            
        Returns:
            执行结果列表
        """
        results = []
        self.is_running = True
        self.start_time = datetime.now()
        self.total_tests = len(test_cases)
        
        print(f"🚀 开始顺序执行 {len(test_cases)} 个测试用例")
        print("=" * 60)
        
        try:
            for i, test_case in enumerate(test_cases):
                if not self.is_running:
                    print("⏹️  测试执行被中断")
                    break

                self.current_test = i + 1

                # 检查仓位管理器，确保币种可用
                coin = test_case.coin
                max_wait_time = 300  # 最大等待5分钟
                wait_start = time.time()

                while not self.position_manager.can_start_new_group(coin):
                    if time.time() - wait_start > max_wait_time:
                        print(f"⚠️  等待{coin}仓位释放超时，跳过测试 #{self.current_test}")
                        result = {
                            'test_case': test_case,
                            'execution_time': 0,
                            'trade_result': {'success': False, 'error': 'position_timeout'},
                            'trade_record': None,
                            'success': False
                        }
                        results.append(result)
                        break

                    print(f"⏳ 等待{coin}仓位释放... (已等待{time.time() - wait_start:.1f}秒)")
                    time.sleep(5)  # 等待5秒后重试
                else:
                    # 执行测试
                    result = self.execute_single_test_with_position_management(test_case)
                    results.append(result)
                
                # 进度回调
                if progress_callback:
                    progress_callback(self.current_test, self.total_tests, result)
                
                # 显示进度
                if self.current_test % 10 == 0 or self.current_test == self.total_tests:
                    elapsed = (datetime.now() - self.start_time).total_seconds()
                    avg_time = elapsed / self.current_test
                    remaining = (self.total_tests - self.current_test) * avg_time
                    
                    print(f"📊 进度: {self.current_test}/{self.total_tests} "
                          f"({self.current_test/self.total_tests*100:.1f}%) "
                          f"成功: {self.execution_stats['successful_tests']} "
                          f"失败: {self.execution_stats['failed_tests']} "
                          f"预计剩余: {remaining/60:.1f}分钟")
                
                # 测试间延迟
                if self.delay_between_tests > 0 and self.current_test < self.total_tests:
                    time.sleep(self.delay_between_tests)
                    
        except KeyboardInterrupt:
            print("\n⏹️  用户中断测试执行")
            self.is_running = False
        
        self.is_running = False
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        print(f"\n🏁 测试执行完成！")
        print("=" * 60)
        print(f"总测试数: {len(test_cases)}")
        print(f"完成测试: {self.execution_stats['completed_tests']}")
        print(f"成功测试: {self.execution_stats['successful_tests']}")
        print(f"失败测试: {self.execution_stats['failed_tests']}")
        print(f"总耗时: {total_duration/60:.1f} 分钟")
        print(f"平均耗时: {total_duration/len(results):.1f} 秒/测试")
        
        return results
    
    def stop_execution(self):
        """停止执行"""
        self.is_running = False
        print("⏹️  正在停止测试执行...")
    
    def get_progress(self) -> Dict[str, Any]:
        """获取执行进度"""
        if not self.start_time:
            return {'status': 'not_started'}
        
        elapsed = (datetime.now() - self.start_time).total_seconds()
        progress_percent = (self.current_test / self.total_tests * 100) if self.total_tests > 0 else 0
        
        return {
            'status': 'running' if self.is_running else 'stopped',
            'current_test': self.current_test,
            'total_tests': self.total_tests,
            'progress_percent': progress_percent,
            'elapsed_seconds': elapsed,
            'successful_tests': self.execution_stats['successful_tests'],
            'failed_tests': self.execution_stats['failed_tests'],
            'coin_stats': self.execution_stats['coin_stats']
        }
    
    def run_full_test_suite(self, total_tests: int = 5000, 
                           coin_distribution: Dict[str, float] = None) -> Dict[str, Any]:
        """运行完整的测试套件
        
        Args:
            total_tests: 总测试数量
            coin_distribution: 币种分布
            
        Returns:
            执行结果摘要
        """
        print(f"🎯 对敲检测测试套件")
        print("=" * 60)
        print(f"目标测试数量: {total_tests}")
        print(f"测试范围: 10U-10万U")
        print(f"时间间隔: 1-120秒")
        print(f"支持币种: DOG(1-100U), ETH(50-5000U), BTC(5000-100000U)")
        print("=" * 60)
        
        # 生成测试用例
        print("🎲 生成测试用例...")
        test_cases = self.generator.generate_test_batch(
            total_cases=total_tests,
            coin_distribution=coin_distribution
        )
        
        # 显示测试用例统计
        self.generator.print_statistics(test_cases)
        
        # 确认执行
        print(f"\n⚠️  即将执行 {len(test_cases)} 个真实交易测试")
        print("这将产生真实的交易费用和市场影响")
        confirm = input("确认继续执行？(输入 'YES' 确认): ").strip()
        
        if confirm != 'YES':
            print("❌ 用户取消测试执行")
            return {'status': 'cancelled'}
        
        # 执行测试
        results = self.execute_batch_sequential(test_cases)
        
        # 生成执行报告
        execution_summary = {
            'test_session_id': datetime.now().strftime('%Y%m%d_%H%M%S'),
            'total_tests': len(test_cases),
            'completed_tests': len(results),
            'successful_tests': self.execution_stats['successful_tests'],
            'failed_tests': self.execution_stats['failed_tests'],
            'success_rate': self.execution_stats['successful_tests'] / len(results) * 100 if results else 0,
            'coin_distribution': self.execution_stats['coin_stats'],
            'execution_time_minutes': (datetime.now() - self.start_time).total_seconds() / 60,
            'errors': self.execution_stats['errors'][:10],  # 只保留前10个错误
            'database_path': self.recorder.db_path
        }
        
        # 保存执行报告
        report_filename = f"test_execution_report_{execution_summary['test_session_id']}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(execution_summary, f, indent=2, ensure_ascii=False)
        
        print(f"📄 执行报告已保存到: {report_filename}")
        
        return execution_summary


def main():
    """主函数"""
    print("🚀 对敲检测测试执行引擎")
    print("=" * 50)
    
    engine = TestExecutionEngine(delay_between_tests=0.5)  # 0.5秒间隔
    
    while True:
        print("\n请选择:")
        print("1. 运行完整测试套件 (5000个测试)")
        print("2. 运行小规模测试 (自定义数量)")
        print("3. 生成测试用例预览")
        print("4. 查看执行进度")
        print("5. 停止当前执行")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            if engine.is_running:
                engine.stop_execution()
            break
        elif choice == '1':
            summary = engine.run_full_test_suite(5000)
            print(f"\n📊 执行摘要:")
            print(json.dumps(summary, indent=2, ensure_ascii=False))
        elif choice == '2':
            try:
                count = int(input("请输入测试数量 (1-1000): "))
                if 1 <= count <= 1000:
                    summary = engine.run_full_test_suite(count)
                    print(f"\n📊 执行摘要:")
                    print(json.dumps(summary, indent=2, ensure_ascii=False))
                else:
                    print("❌ 数量必须在1-1000之间")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '3':
            try:
                count = int(input("请输入预览数量 (1-100): "))
                if 1 <= count <= 100:
                    test_cases = engine.generator.generate_test_batch(count)
                    engine.generator.print_statistics(test_cases)
                    
                    print(f"\n📋 前10个测试用例预览:")
                    for i, case in enumerate(test_cases[:10]):
                        print(f"  {i+1}. {case.description} ({case.test_type})")
                else:
                    print("❌ 数量必须在1-100之间")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '4':
            progress = engine.get_progress()
            print(f"\n📊 执行进度:")
            print(json.dumps(progress, indent=2, ensure_ascii=False))
        elif choice == '5':
            engine.stop_execution()
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
