#!/usr/bin/env python3
"""
测试金额变动功能
"""

from test_data_generator import WashTradingTestGenerator

def test_amount_variation():
    """测试金额变动功能"""
    print("🧪 测试金额变动功能...")
    
    generator = WashTradingTestGenerator(seed=42)  # 固定种子
    
    print("\n📊 生成10个DOG测试用例，检查金额变动...")
    amounts = []
    for i in range(10):
        amount = generator.generate_amount_for_coin('DOG')
        amounts.append(amount)
        print(f"{i+1:2d}. DOG: {amount} USDT")
    
    # 分析变动
    print(f"\n🔍 金额分析:")
    print(f"最小金额: {min(amounts)} USDT")
    print(f"最大金额: {max(amounts)} USDT")
    print(f"平均金额: {sum(amounts)/len(amounts):.2f} USDT")
    
    # 检查是否有变动
    unique_amounts = len(set(amounts))
    print(f"唯一金额数: {unique_amounts}/{len(amounts)}")
    
    if unique_amounts > 1:
        print("✅ 金额有变动")
        
        # 计算变动范围
        variations = []
        for i in range(1, len(amounts)):
            if amounts[i-1] > 0:
                variation = abs(amounts[i] - amounts[i-1]) / amounts[i-1] * 100
                variations.append(variation)
        
        if variations:
            avg_variation = sum(variations) / len(variations)
            max_variation = max(variations)
            print(f"平均变动: {avg_variation:.2f}%")
            print(f"最大变动: {max_variation:.2f}%")
    else:
        print("❌ 金额没有变动")
    
    print("\n📊 测试ETH金额变动...")
    eth_amounts = []
    for i in range(5):
        amount = generator.generate_amount_for_coin('ETH')
        eth_amounts.append(amount)
        print(f"{i+1}. ETH: {amount} USDT")
    
    print("\n📊 测试BTC金额变动...")
    btc_amounts = []
    for i in range(5):
        amount = generator.generate_amount_for_coin('BTC')
        btc_amounts.append(amount)
        print(f"{i+1}. BTC: {amount} USDT")
    
    print("\n✅ 金额变动测试完成！")

if __name__ == "__main__":
    test_amount_variation()
