#!/usr/bin/env python3
"""
测试仓位管理器
"""

from position_manager import PositionManager

def test_position_manager():
    """测试仓位管理器功能"""
    print("🧪 测试仓位管理器...")
    pm = PositionManager()
    
    print("\n📊 初始状态:")
    pm.print_status()
    
    print("\n🚀 开始BTC交易组...")
    success = pm.start_new_group('BTC', 'group_001')
    print(f"开始交易组: {'✅' if success else '❌'}")
    
    print("\n📈 开多头仓位...")
    success = pm.open_long_position('BTC', 'order_001', 10000.0)
    print(f"开多头仓位: {'✅' if success else '❌'}")
    pm.print_status()
    
    print("\n📉 开空头仓位...")
    success = pm.open_short_position('BTC', 'order_002', 10000.0)
    print(f"开空头仓位: {'✅' if success else '❌'}")
    pm.print_status()
    
    print("\n🔄 平多头仓位...")
    success = pm.close_long_position('BTC')
    print(f"平多头仓位: {'✅' if success else '❌'}")
    pm.print_status()
    
    print("\n✅ 平空头仓位...")
    success = pm.close_short_position('BTC')
    print(f"平空头仓位: {'✅' if success else '❌'}")
    pm.print_status()
    
    print("\n🔍 测试并发控制...")
    print(f"BTC可用: {pm.can_start_new_group('BTC')}")
    print(f"ETH可用: {pm.can_start_new_group('ETH')}")
    print(f"可用币种: {pm.get_available_coins()}")
    print(f"忙碌币种: {pm.get_busy_coins()}")
    
    print("\n🧪 测试并发冲突...")
    # 开始ETH交易组
    pm.start_new_group('ETH', 'group_002')
    pm.open_long_position('ETH', 'order_003', 1000.0)
    
    print(f"ETH开仓后 - 可用币种: {pm.get_available_coins()}")
    print(f"ETH开仓后 - 忙碌币种: {pm.get_busy_coins()}")
    
    # 尝试再次开ETH仓位（应该失败）
    success = pm.open_long_position('ETH', 'order_004', 2000.0)
    print(f"重复开ETH多头: {'✅' if success else '❌'} (应该失败)")
    
    # 清理
    pm.close_all_positions('ETH')
    print(f"清理后 - 可用币种: {pm.get_available_coins()}")
    
    print("\n✅ 仓位管理器测试完成！")

if __name__ == "__main__":
    test_position_manager()
