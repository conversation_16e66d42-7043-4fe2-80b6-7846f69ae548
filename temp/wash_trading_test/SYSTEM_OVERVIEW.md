# 对敲检测测试系统 - 完整概览

## 🎯 系统目标

基于您提供的算法文档和交易需求，创建一个完整的对敲检测测试系统，用于验证对敲检测算法的有效性。

## 📋 核心需求实现

### ✅ 已完成的需求

1. **三币种支持**: BTC、ETH、DOG 完整实现
2. **测试范围覆盖**: 10U-10万U 完整覆盖
3. **时间间隔**: 1-120秒 可配置
4. **批量测试**: 支持5000组自动化测试
5. **数据记录**: 完整的交易数据记录和分析
6. **对敲检测**: 基于算法文档的评分机制

### 🎯 币种分工明确

| 币种 | 测试范围 | 最小下单 | 换算比例 | 特殊参数 | 状态 |
|------|----------|----------|----------|----------|------|
| **DOG** | 1-100 USDT | 1U | 1:4 | ppw: "W001" | ✅ 完成 |
| **ETH** | 50-5000 USDT | 50U | 50:1 | ppw: "W001" | ✅ 完成 |
| **BTC** | 5000-100000 USDT | 118U | 10000:86 | ppw: "W001" | ✅ 完成 |

## 🏗️ 系统架构

### 🎯 三个币种的交易引擎特点

| 币种 | 交易引擎 | 换算比例 | 测试范围 | 特殊参数 |
|------|----------|----------|----------|----------|
| **BTC** | `btc_trader.py` | 10000:86 | 5000-10万U | `ppw: "W001"` |
| **ETH** | `eth_trader.py` | 50:1 | 50-5000U | `ppw: "W001"` |
| **DOG** | `dog_trader.py` | 1:4 | 1-100U | `ppw: "W001"` |

**换算说明**:
- **DOG**: 1 USDT = 4 orderQty
- **ETH**: 50 USDT = 1 orderQty
- **BTC**: 10000 USDT = 86 orderQty

**特殊参数**: 所有币种都使用 `ppw: "W001"` 参数

### 核心模块

```
对敲检测测试系统
├── 🚀 启动层
│   ├── start.py              # 启动脚本（推荐）
│   ├── main.py               # 主程序
│   └── config.py             # 系统配置
│
├── 💰 交易层
│   ├── unified_trader.py     # 统一交易接口
│   ├── btc_trader.py         # BTC专用交易模块
│   ├── eth_trader.py         # ETH专用交易模块
│   └── dog_trader.py         # DOG专用交易模块
│
├── 🎲 数据层
│   ├── test_data_generator.py # 测试数据生成
│   ├── trade_recorder.py      # 交易数据记录
│   └── data_analyzer.py       # 数据分析报告
│
├── 🚀 执行层
│   ├── test_execution_engine.py # 测试执行引擎
│   └── test_modules.py          # 模块测试脚本
│
└── 📖 文档层
    ├── README.md             # 使用说明
    ├── SYSTEM_OVERVIEW.md    # 系统概览
    └── 算法文档              # 对敲检测算法文档
```

## 🔄 工作流程

### 1. 测试数据生成
```python
# 生成5000个测试用例
generator = WashTradingTestGenerator()
test_cases = generator.generate_test_batch(5000)

# 智能分布：
# - DOG: 40% (2000个) - 1-100 USDT
# - ETH: 40% (2000个) - 50-5000 USDT  
# - BTC: 20% (1000个) - 5000-100000 USDT
```

### 2. 交易执行
```python
# 统一接口执行
trader = UnifiedTrader()
for test_case in test_cases:
    result = trader.trade_sequence(
        coin=test_case.coin,
        usdt_amount=test_case.usdt_amount,
        wait_seconds=test_case.wait_seconds
    )
```

### 3. 数据记录
```python
# 自动记录到SQLite数据库
recorder = TradeRecorder()
record = recorder.record_trade(result, test_case.case_id, test_case.test_type)
```

### 4. 对敲分析
```python
# 基于算法文档的分析
analyzer = WashTradingDataAnalyzer()
wash_pairs = analyzer.find_potential_wash_trading_pairs()
```

## 📊 对敲检测算法实现

### 评分维度（完全按算法文档）

1. **盈亏对敲分数** (40%权重)
   - 核心特征：双方盈亏抵消程度
   - 计算：基于总盈亏与盈亏总和的比值

2. **时间匹配分数** (25%权重)
   - 同步性特征：开仓时间接近程度
   - 计算：指数衰减函数 `exp(-2 * normalized_diff)`

3. **金额匹配分数** (25%权重)
   - 规模一致性：交易金额相似程度
   - 计算：阶梯式容差，不同金额范围不同标准

4. **持仓时长相似度** (10%权重)
   - 行为一致性：持仓时间相似程度
   - 计算：`1.0 - min(时长差 / 最大时长, 1.0)`

### 风险等级判定

- **Critical**: wash_score > 0.9 且 time_match_score > 0.8
- **High**: wash_score > 0.85
- **Medium**: wash_score > 0.7
- **Low**: wash_score > 0.5
- **Minimal**: wash_score ≤ 0.5

## 🚀 快速开始

### 1. 环境检查
```bash
cd temp/wash_trading_test
python test_modules.py  # 验证所有模块
```

### 2. 启动系统
```bash
python start.py  # 推荐：包含环境检查
# 或
python main.py   # 直接启动
```

### 3. 选择测试模式

**快速验证** (推荐首次使用):
- 选择菜单 "2. 运行快速测试 (5个测试)"
- 验证系统正常工作

**完整测试**:
- 选择菜单 "3. 运行完整测试套件 (5000个测试)"
- 预计耗时：2-4小时

**自定义测试**:
- 选择菜单 "4. 运行自定义测试"
- 自定义数量和币种分布

## 📈 数据输出

### 自动生成文件

1. **数据库**: `wash_trading_test.db`
   - 完整的交易记录
   - 支持SQL查询和分析

2. **交易记录**: `trade_records_YYYYMMDD_HHMMSS.csv/json`
   - 每笔交易的详细信息
   - 包含订单ID、时间、金额等

3. **分析报告**: `wash_trading_analysis_YYYYMMDD_HHMMSS.json/csv`
   - 对敲检测分析结果
   - 风险等级分布和统计

4. **执行报告**: `test_execution_report_YYYYMMDD_HHMMSS.json`
   - 测试执行摘要
   - 成功率、耗时等统计

## ⚠️ 重要提示

### 资金要求
- **DOG测试**: 最少100 USDT (支持1-100U测试)
- **ETH测试**: 最少5000 USDT (支持50-5000U测试)
- **BTC测试**: 最少100000 USDT (支持5000-10万U测试)
- **完整测试**: 建议准备20万USDT以上

### 风险控制
- 所有交易都是真实交易，产生实际费用
- 系统内置安全配置和确认机制
- 支持随时中断和恢复
- 建议分批执行大规模测试

### 性能优化
- 测试间隔可配置（默认0.5秒）
- 支持并发控制避免限流
- 自动重试机制处理临时错误
- 实时进度监控和统计

## 🔧 技术特点

### 模块化设计
- 每个币种独立的交易模块
- 统一的接口和数据格式
- 可扩展的架构设计

### 数据完整性
- SQLite数据库持久化存储
- 完整的交易生命周期记录
- 多格式数据导出支持

### 智能分析
- 基于算法文档的精确实现
- 多维度特征分析
- 自动化风险等级判定

### 用户友好
- 图形化菜单界面
- 详细的进度显示
- 完善的错误处理和提示

## 📞 技术支持

如有问题或需要调整，请联系开发团队。

---

**版本**: 1.0  
**完成时间**: 2025-08-01  
**开发**: Augment Agent  
**状态**: ✅ 完整实现，可立即使用
