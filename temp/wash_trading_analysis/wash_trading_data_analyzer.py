#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对敲数据分析脚本
分析平台抓取数据与脚本生成测试数据的准确率、差值范围等
"""

import json
import pandas as pd
from bs4 import BeautifulSoup
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class WashTradingAnalyzer:
    def __init__(self):
        self.platform_data1 = None
        self.platform_data2 = None
        self.test_data = None
        self.analysis_results = {}
        
    def load_platform_data_from_html(self, html_file: str) -> pd.DataFrame:
        """从HTML文件加载平台数据"""
        print(f"正在加载平台数据: {html_file}")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        table = soup.find('table')
        
        if not table:
            raise ValueError(f"在 {html_file} 中未找到表格")
        
        # 获取表头
        headers = []
        header_row = table.find('tr')
        if header_row:
            for th in header_row.find_all(['th', 'td']):
                headers.append(th.get_text().strip())
        
        # 获取数据行
        rows = []
        for tr in table.find_all('tr')[1:]:  # 跳过表头
            row = []
            for td in tr.find_all(['td', 'th']):
                row.append(td.get_text().strip())
            if row:
                rows.append(row)
        
        df = pd.DataFrame(rows, columns=headers)
        print(f"加载了 {len(df)} 条平台数据记录")
        return df
    
    def load_test_data_from_json(self, json_file: str) -> pd.DataFrame:
        """从JSON文件加载测试数据"""
        print(f"正在加载测试数据: {json_file}")
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        records = data.get('records', [])
        df = pd.DataFrame(records)
        print(f"加载了 {len(df)} 条测试数据记录")
        return df
    
    def standardize_platform_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化平台数据格式"""
        print("正在标准化平台数据...")

        # 根据列名判断是哪个平台数据文件
        if 'order_id' in df.columns:
            # 平台抓取数据1.html的格式
            standardized = pd.DataFrame({
                'id': df['id'],
                'symbol': df['symbol'],
                'uid': df['uid'],
                'order_id': df['order_id'],
                'open_amount': pd.to_numeric(df['open_amount'], errors='coerce'),
                'close_amount': pd.to_numeric(df['close_amount'], errors='coerce'),
                'pnl': pd.to_numeric(df['pnl'], errors='coerce'),
                'net_pnl': pd.to_numeric(df['net_pnl'], errors='coerce'),
                'open_time': pd.to_datetime(df['open_time'], format='%Y-%m-%d %H:%M:%S.%f', errors='coerce'),
                'close_time': pd.to_datetime(df['close_time'], format='%Y-%m-%d %H:%M:%S.%f', errors='coerce'),
                'match_order_id': df['match_order_id'],
                'match_uid': df['match_uid'],
                'match_open_amount': pd.to_numeric(df['match_open_amount'], errors='coerce'),
                'match_close_amount': pd.to_numeric(df['match_close_amount'], errors='coerce'),
                'match_pnl': pd.to_numeric(df['match_pnl'], errors='coerce'),
                'match_net_pnl': pd.to_numeric(df['match_net_pnl'], errors='coerce'),
                'match_open_time': pd.to_datetime(df['match_open_time'], format='%Y-%m-%d %H:%M:%S.%f', errors='coerce'),
                'match_close_time': pd.to_datetime(df['match_close_time'], format='%Y-%m-%d %H:%M:%S.%f', errors='coerce'),
                'score': pd.to_numeric(df['score'], errors='coerce'),
                'create_time': pd.to_datetime(df['create_time'], format='%Y-%m-%d %H:%M:%S.%f', errors='coerce'),
                'brand': df['brand']
            })
        else:
            # 平台抓取数据2.html的格式
            standardized = pd.DataFrame({
                'id': df['ID'],
                'symbol': df['交易对'],
                'uid': df['UID'],
                'order_id': df['订单ID'],
                'open_amount': pd.to_numeric(df['开仓数量U'], errors='coerce'),
                'close_amount': pd.to_numeric(df['平仓数量U'], errors='coerce'),
                'pnl': pd.to_numeric(df['平仓盈利'], errors='coerce'),
                'net_pnl': pd.to_numeric(df['净盈利'], errors='coerce'),
                'open_time': pd.to_datetime(df['开仓时间'], errors='coerce'),
                'close_time': pd.to_datetime(df['平仓时间'], errors='coerce'),
                'match_order_id': df['对手订单ID'],
                'match_uid': df['对手UID'],
                'match_open_amount': pd.to_numeric(df['对手开仓数量U'], errors='coerce'),
                'match_close_amount': pd.to_numeric(df['对手平仓数量U'], errors='coerce'),
                'match_pnl': pd.to_numeric(df['对手平仓盈利'], errors='coerce'),
                'match_net_pnl': pd.to_numeric(df['对手净盈利'], errors='coerce'),
                'match_open_time': pd.to_datetime(df['对手开仓时间'], errors='coerce'),
                'match_close_time': pd.to_datetime(df['对手平仓时间'], errors='coerce'),
                'score': pd.to_numeric(df['评分'], errors='coerce'),
                'create_time': pd.to_datetime(df['识别时间'], errors='coerce'),
                'brand': 'Unknown'  # 平台数据2没有品牌信息
            })

        print(f"标准化后数据: {len(standardized)} 条记录")

        # 打印时间范围信息
        if not standardized['open_time'].isna().all():
            print(f"平台数据时间范围: {standardized['open_time'].min()} 到 {standardized['close_time'].max()}")

        return standardized
    
    def standardize_test_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化测试数据格式"""
        print("正在标准化测试数据...")
        
        standardized = pd.DataFrame({
            'record_id': df['record_id'],
            'test_case_id': df['test_case_id'],
            'coin': df['coin'],
            'symbol': df['symbol'],
            'usdt_amount': pd.to_numeric(df['usdt_amount'], errors='coerce'),
            'orderqty': pd.to_numeric(df['orderqty'], errors='coerce'),
            'buy_order_id': df['buy_order_id'],
            'sell_order_id': df['sell_order_id'],
            'buy_time': pd.to_datetime(df['buy_time'], errors='coerce'),
            'sell_time': pd.to_datetime(df['sell_time'], errors='coerce'),
            'total_duration_seconds': pd.to_numeric(df['total_duration_seconds'], errors='coerce'),
            'test_type': df['test_type'],
            'notes': df['notes']
        })
        
        print(f"标准化后测试数据: {len(standardized)} 条记录")
        return standardized

    def analyze_time_overlap(self) -> Dict[str, Any]:
        """分析时间重叠情况"""
        print("正在分析时间重叠...")
        
        # 合并所有平台数据
        all_platform_data = []
        if self.platform_data1 is not None:
            all_platform_data.append(self.platform_data1)
        if self.platform_data2 is not None:
            all_platform_data.append(self.platform_data2)
        
        if not all_platform_data:
            return {"error": "没有平台数据"}
        
        platform_combined = pd.concat(all_platform_data, ignore_index=True)
        
        # 获取时间范围
        platform_time_range = {
            'start': platform_combined['open_time'].min(),
            'end': platform_combined['close_time'].max()
        }
        
        test_time_range = {
            'start': self.test_data['buy_time'].min(),
            'end': self.test_data['sell_time'].max()
        }
        
        # 计算重叠
        overlap_start = max(platform_time_range['start'], test_time_range['start'])
        overlap_end = min(platform_time_range['end'], test_time_range['end'])
        
        has_overlap = overlap_start <= overlap_end
        
        return {
            'platform_time_range': platform_time_range,
            'test_time_range': test_time_range,
            'has_overlap': has_overlap,
            'overlap_start': overlap_start if has_overlap else None,
            'overlap_end': overlap_end if has_overlap else None,
            'platform_records': len(platform_combined),
            'test_records': len(self.test_data)
        }

    def analyze_amount_distribution(self) -> Dict[str, Any]:
        """分析金额分布"""
        print("正在分析金额分布...")
        
        # 合并所有平台数据
        all_platform_data = []
        if self.platform_data1 is not None:
            all_platform_data.append(self.platform_data1)
        if self.platform_data2 is not None:
            all_platform_data.append(self.platform_data2)
        
        platform_combined = pd.concat(all_platform_data, ignore_index=True)
        
        # 平台数据金额统计
        platform_amounts = platform_combined['open_amount'].dropna()
        platform_stats = {
            'count': len(platform_amounts),
            'mean': platform_amounts.mean(),
            'median': platform_amounts.median(),
            'std': platform_amounts.std(),
            'min': platform_amounts.min(),
            'max': platform_amounts.max(),
            'q25': platform_amounts.quantile(0.25),
            'q75': platform_amounts.quantile(0.75)
        }
        
        # 测试数据金额统计
        test_amounts = self.test_data['usdt_amount'].dropna()
        test_stats = {
            'count': len(test_amounts),
            'mean': test_amounts.mean(),
            'median': test_amounts.median(),
            'std': test_amounts.std(),
            'min': test_amounts.min(),
            'max': test_amounts.max(),
            'q25': test_amounts.quantile(0.25),
            'q75': test_amounts.quantile(0.75)
        }
        
        return {
            'platform_stats': platform_stats,
            'test_stats': test_stats,
            'platform_amounts': platform_amounts.tolist(),
            'test_amounts': test_amounts.tolist()
        }

    def analyze_symbol_distribution(self) -> Dict[str, Any]:
        """分析交易对分布"""
        print("正在分析交易对分布...")

        # 合并所有平台数据
        all_platform_data = []
        if self.platform_data1 is not None:
            all_platform_data.append(self.platform_data1)
        if self.platform_data2 is not None:
            all_platform_data.append(self.platform_data2)

        platform_combined = pd.concat(all_platform_data, ignore_index=True)

        # 平台数据交易对分布
        platform_symbols = platform_combined['symbol'].value_counts().to_dict()

        # 测试数据交易对分布（需要转换格式）
        test_symbol_mapping = {
            'DOG': 'DOGE-USDT',
            'BTC': 'BTC-USDT',
            'ETH': 'ETH-USDT'
        }

        test_symbols_raw = self.test_data['coin'].value_counts()
        test_symbols = {}
        for coin, count in test_symbols_raw.items():
            symbol = test_symbol_mapping.get(coin, f"{coin}-USDT")
            test_symbols[symbol] = count

        return {
            'platform_symbols': platform_symbols,
            'test_symbols': test_symbols
        }

    def calculate_amount_tolerance(self, amount_a: float, amount_b: float) -> float:
        """根据算法文档计算金额容差"""
        max_amount = max(amount_a, amount_b)

        if max_amount <= 100:
            # 极小额：10%相对容差，最小2 USDT
            return max(max_amount * 0.10, 2.0)
        elif max_amount <= 1000:
            # 小额：5%相对容差，最小5 USDT
            return max(max_amount * 0.05, 5.0)
        elif max_amount <= 10000:
            # 中额：3%相对容差或50 USDT，取较小值
            return min(max_amount * 0.03, 50.0)
        elif max_amount <= 100000:
            # 大额：2%相对容差或200 USDT，取较小值
            return min(max_amount * 0.02, 200.0)
        else:
            # 超大额：1.5%相对容差
            return max_amount * 0.015

    def calculate_amount_match_score(self, amount_a: float, amount_b: float) -> float:
        """根据算法文档计算金额匹配分数"""
        amount_diff = abs(amount_a - amount_b)
        max_amount = max(amount_a, amount_b)

        if max_amount == 0:
            return 0.0

        tolerance = self.calculate_amount_tolerance(amount_a, amount_b)

        if amount_diff == 0:
            return 1.0
        elif amount_diff <= tolerance:
            # 容差内：0.80-1.0 的线性评分
            score = 1.0 - 0.2 * (amount_diff / tolerance)
        else:
            # 容差外：0-0.8 的指数衰减评分
            excess_ratio = (amount_diff - tolerance) / tolerance
            score = 0.80 * np.exp(-excess_ratio * 0.75)

        return max(0.0, min(1.0, score))

    def calculate_time_match_score(self, time_diff_seconds: float, time_window: float = 30.0) -> float:
        """根据算法文档计算时间匹配分数"""
        normalized_diff = time_diff_seconds / time_window

        if normalized_diff >= 1.0:
            return 0.0
        else:
            return np.exp(-2 * normalized_diff)  # 指数衰减

    def find_potential_matches_by_open_time(self) -> Dict[str, Any]:
        """基于开仓时间的匹配（算法文档标准）"""
        return self._find_matches_by_time_type('open', '开仓时间')

    def find_potential_matches_by_close_time(self) -> Dict[str, Any]:
        """基于平仓时间的匹配（平台引擎实际）"""
        return self._find_matches_by_time_type('close', '平仓时间')

    def find_order_id_matches(self) -> Dict[str, Any]:
        """通过订单ID匹配测试数据和平台数据"""
        print("正在通过订单ID进行精确匹配...")

        # 合并所有平台数据
        all_platform_data = []
        if self.platform_data1 is not None:
            all_platform_data.append(self.platform_data1)
        if self.platform_data2 is not None:
            all_platform_data.append(self.platform_data2)

        platform_combined = pd.concat(all_platform_data, ignore_index=True)

        exact_matches = []
        potential_matches = []
        unmatched_tests = []

        print(f"平台数据总数: {len(platform_combined)}")
        print(f"测试数据总数: {len(self.test_data)}")

        # 调试信息：查看数据样本
        print("\n=== 数据样本分析 ===")
        print("测试数据订单ID样本:")
        for i in range(min(3, len(self.test_data))):
            test_record = self.test_data.iloc[i]
            print(f"  买单ID: {test_record['buy_order_id']} (类型: {type(test_record['buy_order_id'])})")
            print(f"  卖单ID: {test_record['sell_order_id']} (类型: {type(test_record['sell_order_id'])})")

        print("\n平台数据订单ID样本:")
        for i in range(min(3, len(platform_combined))):
            platform_record = platform_combined.iloc[i]
            print(f"  订单ID: {platform_record['order_id']} (类型: {type(platform_record['order_id'])})")
            print(f"  匹配订单ID: {platform_record['match_order_id']} (类型: {type(platform_record['match_order_id'])})")

        print("\n平台数据时间范围:")
        if not platform_combined['open_time'].isna().all():
            print(f"  开仓时间: {platform_combined['open_time'].min()} 到 {platform_combined['open_time'].max()}")
        if not platform_combined['close_time'].isna().all():
            print(f"  平仓时间: {platform_combined['close_time'].min()} 到 {platform_combined['close_time'].max()}")

        print("\n测试数据时间范围:")
        test_buy_times = pd.to_datetime(self.test_data['buy_time'])
        test_sell_times = pd.to_datetime(self.test_data['sell_time'])
        print(f"  买入时间: {test_buy_times.min()} 到 {test_buy_times.max()}")
        print(f"  卖出时间: {test_sell_times.min()} 到 {test_sell_times.max()}")

        # 为每个测试记录寻找对应的平台记录
        for _, test_record in self.test_data.iterrows():
            buy_order_id = str(test_record['buy_order_id'])
            sell_order_id = str(test_record['sell_order_id'])

            # 寻找匹配的平台记录
            # 方式1: buy_order_id 匹配 order_id, sell_order_id 匹配 match_order_id
            match1 = platform_combined[
                (platform_combined['order_id'].astype(str) == buy_order_id) &
                (platform_combined['match_order_id'].astype(str) == sell_order_id)
            ]

            # 方式2: sell_order_id 匹配 order_id, buy_order_id 匹配 match_order_id
            match2 = platform_combined[
                (platform_combined['order_id'].astype(str) == sell_order_id) &
                (platform_combined['match_order_id'].astype(str) == buy_order_id)
            ]

            # 方式3: 只匹配其中一个订单ID
            partial_match1 = platform_combined[platform_combined['order_id'].astype(str) == buy_order_id]
            partial_match2 = platform_combined[platform_combined['order_id'].astype(str) == sell_order_id]
            partial_match3 = platform_combined[platform_combined['match_order_id'].astype(str) == buy_order_id]
            partial_match4 = platform_combined[platform_combined['match_order_id'].astype(str) == sell_order_id]

            matched = False

            # 处理完全匹配
            for match_df in [match1, match2]:
                if not match_df.empty:
                    for _, platform_record in match_df.iterrows():
                        match_info = self.create_match_info(test_record, platform_record, 'exact_order_match')
                        exact_matches.append(match_info)
                        matched = True

            # 处理部分匹配
            if not matched:
                for match_df in [partial_match1, partial_match2, partial_match3, partial_match4]:
                    if not match_df.empty:
                        for _, platform_record in match_df.iterrows():
                            match_info = self.create_match_info(test_record, platform_record, 'partial_order_match')
                            potential_matches.append(match_info)
                            matched = True
                        break  # 只取第一个部分匹配

            # 记录未匹配的测试数据
            if not matched:
                unmatched_tests.append({
                    'test_record_id': test_record['record_id'],
                    'buy_order_id': buy_order_id,
                    'sell_order_id': sell_order_id,
                    'coin': test_record['coin'],
                    'usdt_amount': test_record['usdt_amount'],
                    'buy_time': test_record['buy_time'],
                    'sell_time': test_record['sell_time']
                })

        print(f"\n=== 匹配结果 ===")
        print(f"精确匹配: {len(exact_matches)} 条")
        print(f"部分匹配: {len(potential_matches)} 条")
        print(f"未匹配: {len(unmatched_tests)} 条")

        return {
            'exact_matches': exact_matches,
            'potential_matches': potential_matches,
            'unmatched_tests': unmatched_tests,
            'match_count': len(exact_matches),
            'potential_count': len(potential_matches),
            'unmatched_count': len(unmatched_tests),
            'test_total': len(self.test_data),
            'platform_total': len(platform_combined),
            'match_type': 'order_id'
        }

    def create_match_info(self, test_record, platform_record, match_type: str) -> Dict:
        """创建匹配信息"""
        # 时间差异计算
        test_buy_time = pd.to_datetime(test_record['buy_time'])
        test_sell_time = pd.to_datetime(test_record['sell_time'])
        platform_open_time = platform_record['open_time']
        platform_close_time = platform_record['close_time']

        time_diff_open = abs((platform_open_time - test_buy_time).total_seconds()) if pd.notna(platform_open_time) else float('inf')
        time_diff_close = abs((platform_close_time - test_sell_time).total_seconds()) if pd.notna(platform_close_time) else float('inf')

        # 金额差异计算
        test_amount = test_record['usdt_amount']
        platform_amount = platform_record['open_amount']
        amount_diff = abs(platform_amount - test_amount)
        amount_tolerance = self.calculate_amount_tolerance(test_amount, platform_amount)
        amount_match_score = self.calculate_amount_match_score(test_amount, platform_amount)

        # 时间匹配分数
        time_window = 30.0
        time_match_score_open = self.calculate_time_match_score(time_diff_open, time_window)
        time_match_score_close = self.calculate_time_match_score(time_diff_close, time_window)

        # 盈亏对敲分数（使用平台数据的实际盈亏）
        if pd.notna(platform_record['pnl']) and pd.notna(platform_record['match_pnl']):
            # 计算真实的盈亏对敲分数
            profit_a = platform_record['pnl']
            profit_b = platform_record['match_pnl']
            total_profit = profit_a + profit_b
            profit_sum = abs(profit_a) + abs(profit_b)

            if abs(total_profit) == 0:
                profit_hedge_score = 0.8
            elif profit_sum > 0:
                profit_hedge_score = 1.0 - abs(total_profit) / profit_sum
            else:
                profit_hedge_score = 0.5
        else:
            profit_hedge_score = 0.7  # 默认值

        # 综合评分
        overall_score = (profit_hedge_score * 0.2 +
                        amount_match_score * 0.35 +
                        time_match_score_close * 0.35 +
                        time_match_score_open * 0.1)

        return {
            'test_record_id': test_record['record_id'],
            'platform_record_id': platform_record['id'],
            'test_buy_order_id': test_record['buy_order_id'],
            'test_sell_order_id': test_record['sell_order_id'],
            'platform_order_id': platform_record['order_id'],
            'platform_match_order_id': platform_record['match_order_id'],
            'symbol': test_record['coin'],
            'test_amount': test_amount,
            'platform_amount': platform_amount,
            'amount_diff': amount_diff,
            'amount_tolerance': amount_tolerance,
            'amount_match_score': amount_match_score,
            'profit_hedge_score': profit_hedge_score,
            'platform_pnl': platform_record['pnl'],
            'platform_match_pnl': platform_record['match_pnl'],
            'time_diff_open_sec': time_diff_open,
            'time_diff_close_sec': time_diff_close,
            'primary_time_match_score': time_match_score_close,
            'secondary_time_match_score': time_match_score_open,
            'test_buy_time': test_buy_time,
            'test_sell_time': test_sell_time,
            'platform_open_time': platform_open_time,
            'platform_close_time': platform_close_time,
            'overall_score': overall_score,
            'match_type': match_type,
            'primary_time_match': time_diff_close <= 30,
            'amount_match': amount_diff <= amount_tolerance,
            'overall_match': match_type == 'exact_order_match'
        }

    def _find_matches_by_time_type(self, time_type: str, time_desc: str) -> Dict[str, Any]:
        """保留原有的时间匹配方法作为备用"""
        # 现在主要使用订单ID匹配，这个方法作为备用
        return self.find_order_id_matches()

    def find_potential_matches(self) -> Dict[str, Any]:
        """基于订单ID的精确匹配分析"""
        print("正在进行匹配分析...")

        # 使用订单ID进行精确匹配
        order_id_matches = self.find_order_id_matches()

        return order_id_matches

    def calculate_risk_score_distribution(self, matches: List[Dict]) -> Dict[str, Any]:
        """分析风险评分分布"""
        if not matches:
            return {}

        scores = [match['overall_score'] for match in matches]

        # 风险等级分布（根据算法文档）
        risk_levels = {
            'Critical': len([s for s in scores if s >= 0.9]),  # 极高风险
            'High': len([s for s in scores if 0.85 <= s < 0.9]),  # 高风险
            'Medium': len([s for s in scores if 0.7 <= s < 0.85]),  # 中风险
            'Low': len([s for s in scores if 0.5 <= s < 0.7]),  # 低风险
            'Minimal': len([s for s in scores if s < 0.5])  # 极低风险
        }

        return {
            'score_distribution': {
                'mean': np.mean(scores),
                'median': np.median(scores),
                'std': np.std(scores),
                'min': np.min(scores),
                'max': np.max(scores),
                'q25': np.percentile(scores, 25),
                'q75': np.percentile(scores, 75)
            },
            'risk_levels': risk_levels,
            'total_matches': len(matches)
        }

    def analyze_algorithm_performance(self) -> Dict[str, Any]:
        """分析算法性能指标（基于平仓时间匹配）"""
        print("正在分析算法性能指标...")

        match_results = self.find_potential_matches()

        total_test_records = match_results['test_total']
        exact_matches = match_results['match_count']
        potential_matches = match_results['potential_count']

        # 计算检测率
        detection_rate = (exact_matches / total_test_records * 100) if total_test_records > 0 else 0
        potential_detection_rate = (potential_matches / total_test_records * 100) if total_test_records > 0 else 0

        # 风险评分分析
        risk_analysis = {}
        if match_results['exact_matches']:
            risk_analysis['exact'] = self.calculate_risk_score_distribution(match_results['exact_matches'])
        if match_results['potential_matches']:
            risk_analysis['potential'] = self.calculate_risk_score_distribution(match_results['potential_matches'])

        # 算法标准符合度分析
        algorithm_compliance = {}
        if match_results['exact_matches']:
            exact_df = pd.DataFrame(match_results['exact_matches'])

            # 时间窗口符合度（30秒内）
            time_compliant = len(exact_df[exact_df['primary_time_match'] == True])
            time_compliance_rate = (time_compliant / len(exact_df) * 100) if len(exact_df) > 0 else 0

            # 金额容差符合度
            amount_compliant = len(exact_df[exact_df['amount_match'] == True])
            amount_compliance_rate = (amount_compliant / len(exact_df) * 100) if len(exact_df) > 0 else 0

            # 综合评分符合度（≥0.7）
            score_compliant = len(exact_df[exact_df['overall_score'] >= 0.7])
            score_compliance_rate = (score_compliant / len(exact_df) * 100) if len(exact_df) > 0 else 0

            algorithm_compliance = {
                'time_compliance_rate': time_compliance_rate,
                'amount_compliance_rate': amount_compliance_rate,
                'score_compliance_rate': score_compliance_rate,
                'avg_amount_match_score': exact_df['amount_match_score'].mean(),
                'avg_primary_time_match_score': exact_df['primary_time_match_score'].mean(),
                'avg_profit_hedge_score': exact_df['profit_hedge_score'].mean(),
                'avg_overall_score': exact_df['overall_score'].mean()
            }

        # 匹配质量分析
        match_quality = {}
        if match_results['exact_matches']:
            exact_matches_df = pd.DataFrame(match_results['exact_matches'])
            match_quality['exact'] = {
                'avg_amount_diff': exact_matches_df['amount_diff'].mean(),
                'avg_amount_tolerance': exact_matches_df['amount_tolerance'].mean(),
                'avg_time_diff_open': exact_matches_df['time_diff_open_sec'].mean(),
                'avg_time_diff_close': exact_matches_df['time_diff_close_sec'].mean(),
                'avg_primary_time_diff': exact_matches_df['primary_time_diff_sec'].mean(),
                'avg_amount_match_score': exact_matches_df['amount_match_score'].mean(),
                'avg_primary_time_match_score': exact_matches_df['primary_time_match_score'].mean(),
                'avg_profit_hedge_score': exact_matches_df['profit_hedge_score'].mean()
            }

        return {
            'detection_rate_pct': detection_rate,
            'potential_detection_rate_pct': potential_detection_rate,
            'exact_matches': exact_matches,
            'potential_matches': potential_matches,
            'total_test_records': total_test_records,
            'risk_analysis': risk_analysis,
            'algorithm_compliance': algorithm_compliance,
            'match_quality': match_quality,
            'match_details': match_results
        }

    def generate_visualizations(self):
        """生成可视化图表"""
        print("正在生成可视化图表...")

        # 创建图表目录
        import os
        os.makedirs('charts', exist_ok=True)

        # 1. 金额分布对比图
        plt.figure(figsize=(15, 5))

        plt.subplot(1, 3, 1)
        amount_analysis = self.analysis_results['amount_analysis']
        platform_amounts = amount_analysis['platform_amounts']
        test_amounts = amount_analysis['test_amounts']

        plt.hist(platform_amounts, bins=50, alpha=0.7, label='平台数据', color='blue')
        plt.hist(test_amounts, bins=50, alpha=0.7, label='测试数据', color='red')
        plt.xlabel('金额 (USDT)')
        plt.ylabel('频次')
        plt.title('金额分布对比')
        plt.legend()
        plt.yscale('log')

        # 2. 交易对分布对比图
        plt.subplot(1, 3, 2)
        symbol_analysis = self.analysis_results['symbol_analysis']

        symbols = list(set(list(symbol_analysis['platform_symbols'].keys()) +
                          list(symbol_analysis['test_symbols'].keys())))
        platform_counts = [symbol_analysis['platform_symbols'].get(s, 0) for s in symbols]
        test_counts = [symbol_analysis['test_symbols'].get(s, 0) for s in symbols]

        x = np.arange(len(symbols))
        width = 0.35

        plt.bar(x - width/2, platform_counts, width, label='平台数据', color='blue', alpha=0.7)
        plt.bar(x + width/2, test_counts, width, label='测试数据', color='red', alpha=0.7)
        plt.xlabel('交易对')
        plt.ylabel('记录数')
        plt.title('交易对分布对比')
        plt.xticks(x, symbols, rotation=45)
        plt.legend()

        # 3. 算法性能指标图
        plt.subplot(1, 3, 3)
        algo_performance = self.analysis_results['algorithm_performance']

        categories = ['检测率', '潜在检测率']
        rates = [algo_performance['detection_rate_pct'],
                algo_performance['potential_detection_rate_pct']]

        bars = plt.bar(categories, rates, color=['green', 'orange'], alpha=0.7)
        plt.ylabel('检测率 (%)')
        plt.title('算法检测性能')
        plt.ylim(0, max(100, max(rates) * 1.1))

        # 在柱状图上添加数值标签
        for bar, rate in zip(bars, rates):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{rate:.1f}%', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('charts/wash_trading_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 4. 详细的匹配分析图
        if self.analysis_results['algorithm_performance']['match_details']['exact_matches']:
            plt.figure(figsize=(15, 8))

            matches_df = pd.DataFrame(self.analysis_results['algorithm_performance']['match_details']['exact_matches'])

            # 金额匹配散点图
            plt.subplot(2, 3, 1)
            plt.scatter(matches_df['test_amount'], matches_df['platform_amount'], alpha=0.6)
            plt.plot([matches_df['test_amount'].min(), matches_df['test_amount'].max()],
                    [matches_df['test_amount'].min(), matches_df['test_amount'].max()],
                    'r--', label='完美匹配线')
            plt.xlabel('测试数据金额')
            plt.ylabel('平台数据金额')
            plt.title('金额匹配散点图')
            plt.legend()

            # 金额差异分布
            plt.subplot(2, 3, 2)
            plt.hist(matches_df['amount_diff'], bins=20, alpha=0.7, color='green')
            plt.xlabel('金额差异 (USDT)')
            plt.ylabel('频次')
            plt.title('金额差异分布')

            # 时间差异分布
            plt.subplot(2, 3, 3)
            plt.hist(matches_df['time_diff_open_sec'], bins=20, alpha=0.7, color='blue')
            plt.xlabel('开仓时间差异 (秒)')
            plt.ylabel('频次')
            plt.title('时间差异分布')

            # 金额匹配分数分布
            plt.subplot(2, 3, 4)
            plt.hist(matches_df['amount_match_score'], bins=20, alpha=0.7, color='purple')
            plt.xlabel('金额匹配分数')
            plt.ylabel('频次')
            plt.title('金额匹配分数分布')

            # 时间匹配分数分布
            plt.subplot(2, 3, 5)
            plt.hist(matches_df['primary_time_match_score'], bins=20, alpha=0.7, color='orange')
            plt.xlabel('时间匹配分数')
            plt.ylabel('频次')
            plt.title('时间匹配分数分布')

            # 综合评分分布
            plt.subplot(2, 3, 6)
            plt.hist(matches_df['overall_score'], bins=20, alpha=0.7, color='red')
            plt.xlabel('综合评分')
            plt.ylabel('频次')
            plt.title('综合评分分布')
            plt.axvline(x=0.7, color='black', linestyle='--', label='阈值 0.7')
            plt.legend()

            plt.tight_layout()
            plt.savefig('charts/algorithm_performance_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()

    def generate_report(self) -> str:
        """生成分析报告"""
        print("正在生成分析报告...")

        report = []
        report.append("=" * 60)
        report.append("对敲数据分析报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 数据概览
        report.append("1. 数据概览")
        report.append("-" * 30)
        time_analysis = self.analysis_results['time_analysis']
        report.append(f"平台数据记录数: {time_analysis['platform_records']:,}")
        report.append(f"测试数据记录数: {time_analysis['test_records']:,}")
        report.append(f"时间范围重叠: {'是' if time_analysis['has_overlap'] else '否'}")
        report.append("")

        # 金额分析
        report.append("2. 金额分析")
        report.append("-" * 30)
        amount_analysis = self.analysis_results['amount_analysis']
        platform_stats = amount_analysis['platform_stats']
        test_stats = amount_analysis['test_stats']

        report.append(f"平台数据金额统计:")
        report.append(f"  平均值: {platform_stats['mean']:,.2f} USDT")
        report.append(f"  中位数: {platform_stats['median']:,.2f} USDT")
        report.append(f"  标准差: {platform_stats['std']:,.2f} USDT")
        report.append(f"  范围: {platform_stats['min']:,.2f} - {platform_stats['max']:,.2f} USDT")
        report.append("")

        report.append(f"测试数据金额统计:")
        report.append(f"  平均值: {test_stats['mean']:,.2f} USDT")
        report.append(f"  中位数: {test_stats['median']:,.2f} USDT")
        report.append(f"  标准差: {test_stats['std']:,.2f} USDT")
        report.append(f"  范围: {test_stats['min']:,.2f} - {test_stats['max']:,.2f} USDT")
        report.append("")

        # 交易对分析
        report.append("3. 交易对分析")
        report.append("-" * 30)
        symbol_analysis = self.analysis_results['symbol_analysis']

        report.append("平台数据交易对分布:")
        for symbol, count in symbol_analysis['platform_symbols'].items():
            report.append(f"  {symbol}: {count:,} 条记录")
        report.append("")

        report.append("测试数据交易对分布:")
        for symbol, count in symbol_analysis['test_symbols'].items():
            report.append(f"  {symbol}: {count:,} 条记录")
        report.append("")

        # 算法性能分析
        report.append("4. 算法性能分析（基于订单ID精确匹配）")
        report.append("-" * 50)
        algo_performance = self.analysis_results['algorithm_performance']

        report.append(f"精确匹配率: {algo_performance['detection_rate_pct']:.2f}%")
        report.append(f"部分匹配率: {algo_performance['potential_detection_rate_pct']:.2f}%")
        report.append(f"精确匹配数量: {algo_performance['exact_matches']:,}")
        report.append(f"部分匹配数量: {algo_performance['potential_matches']:,}")

        # 添加未匹配数据分析
        if 'unmatched_count' in algo_performance['match_details']:
            unmatched_count = algo_performance['match_details']['unmatched_count']
            unmatched_rate = (unmatched_count / algo_performance['total_test_records'] * 100)
            report.append(f"未匹配数量: {unmatched_count:,}")
            report.append(f"未匹配率: {unmatched_rate:.2f}%")

        report.append("")

        # 算法标准符合度
        if algo_performance.get('algorithm_compliance'):
            compliance = algo_performance['algorithm_compliance']
            report.append("算法标准符合度:")
            report.append(f"  时间窗口符合度: {compliance['time_compliance_rate']:.1f}%")
            report.append(f"  金额容差符合度: {compliance['amount_compliance_rate']:.1f}%")
            report.append(f"  综合评分符合度: {compliance['score_compliance_rate']:.1f}%")
            report.append(f"  平均盈亏对敲分数: {compliance['avg_profit_hedge_score']:.3f}")
            report.append(f"  平均金额匹配分数: {compliance['avg_amount_match_score']:.3f}")
            report.append(f"  平均时间匹配分数: {compliance['avg_primary_time_match_score']:.3f}")
            report.append(f"  平均综合评分: {compliance['avg_overall_score']:.3f}")
            report.append("")

        # 风险等级分布
        if algo_performance.get('risk_analysis', {}).get('exact'):
            risk_analysis = algo_performance['risk_analysis']['exact']
            report.append("风险等级分布:")
            for level, count in risk_analysis['risk_levels'].items():
                percentage = (count / risk_analysis['total_matches'] * 100) if risk_analysis['total_matches'] > 0 else 0
                report.append(f"  {level}: {count} 条 ({percentage:.1f}%)")
            report.append("")

        # 匹配质量分析
        if algo_performance.get('match_quality', {}).get('exact'):
            quality = algo_performance['match_quality']['exact']
            report.append("匹配质量分析:")
            report.append(f"  平均金额差异: {quality['avg_amount_diff']:.2f} USDT")
            report.append(f"  平均金额容差: {quality['avg_amount_tolerance']:.2f} USDT")
            report.append(f"  平均平仓时间差异: {quality['avg_primary_time_diff']:.1f} 秒")
            report.append(f"  平均开仓时间差异: {quality['avg_time_diff_open']:.1f} 秒")
            report.append(f"  平均平仓时间差异: {quality['avg_time_diff_close']:.1f} 秒")
            report.append("")

        # 匹配方法说明
        report.append("匹配方法说明:")
        report.append("  使用订单ID进行精确匹配:")
        report.append("  - 精确匹配: buy_order_id ↔ order_id 且 sell_order_id ↔ match_order_id")
        report.append("  - 部分匹配: 只有一个订单ID匹配")
        report.append("  - 盈亏对敲分数: 使用平台数据的真实盈亏计算")
        report.append("")

        # 权重配置说明
        report.append("权重配置说明:")
        report.append("  盈亏对敲权重: 20% (使用真实盈亏数据)")
        report.append("  金额匹配权重: 35% (提高10%)")
        report.append("  平仓时间权重: 35% (提高10%)")
        report.append("  开仓时间权重: 10%")
        report.append("")

        # 结论和建议
        report.append("5. 结论和优化建议")
        report.append("-" * 40)

        detection_rate = algo_performance['detection_rate_pct']

        # 基于平台引擎实际使用方法的评估
        if detection_rate >= 80:
            conclusion = "平台引擎检测性能优秀，符合预期标准"
            suggestions = ["继续监控性能稳定性", "可考虑进一步优化参数精度"]
        elif detection_rate >= 60:
            conclusion = "平台引擎检测性能良好，有优化空间"
            suggestions = ["优化平仓时间窗口参数", "调整金额容差配置", "考虑获取真实盈亏数据"]
        elif detection_rate >= 40:
            conclusion = "平台引擎检测性能中等，需要重点优化"
            suggestions = ["重新校准平仓时间匹配算法", "优化金额匹配容差策略", "调整综合评分阈值"]
        elif detection_rate >= 20:
            conclusion = "平台引擎检测性能较低，需要重大改进"
            suggestions = ["检查平仓时间数据预处理逻辑", "重新设计匹配算法", "优化特征权重配置"]
        else:
            conclusion = "平台引擎检测性能严重不足，需要全面重构"
            suggestions = ["全面审查算法逻辑", "重新设计检测策略", "增加更多匹配维度"]

        report.append(f"基于检测率 {detection_rate:.2f}%，{conclusion}")
        report.append("")

        report.append("优化建议:")
        for i, suggestion in enumerate(suggestions, 1):
            report.append(f"  {i}. {suggestion}")
        report.append("")

        # 关键发现
        report.append("关键发现:")
        report.append("• 使用订单ID进行精确匹配，避免了时间和金额的模糊匹配")
        report.append("• 平台数据包含真实的盈亏信息，可以计算准确的盈亏对敲分数")
        report.append("• 精确匹配能够准确识别测试数据在平台中的检测情况")
        if 'unmatched_count' in algo_performance['match_details']:
            unmatched_rate = (algo_performance['match_details']['unmatched_count'] / algo_performance['total_test_records'] * 100)
            if unmatched_rate > 50:
                report.append(f"• 高未匹配率({unmatched_rate:.1f}%)表明平台可能未检测到大部分测试交易")
            else:
                report.append(f"• 未匹配率({unmatched_rate:.1f}%)在可接受范围内")
        report.append("")

        return "\n".join(report)

    def run_analysis(self, platform_file1: str, platform_file2: str, test_file: str):
        """运行完整分析"""
        print("开始对敲数据分析...")

        # 加载数据
        platform_df1 = self.load_platform_data_from_html(platform_file1)
        platform_df2 = self.load_platform_data_from_html(platform_file2)
        test_df = self.load_test_data_from_json(test_file)

        # 标准化数据
        self.platform_data1 = self.standardize_platform_data(platform_df1)
        self.platform_data2 = self.standardize_platform_data(platform_df2)
        self.test_data = self.standardize_test_data(test_df)

        # 执行各项分析
        self.analysis_results['time_analysis'] = self.analyze_time_overlap()
        self.analysis_results['amount_analysis'] = self.analyze_amount_distribution()
        self.analysis_results['symbol_analysis'] = self.analyze_symbol_distribution()
        self.analysis_results['algorithm_performance'] = self.analyze_algorithm_performance()

        return self.analysis_results

if __name__ == "__main__":
    analyzer = WashTradingAnalyzer()

    # 文件路径
    platform_file1 = "../../平台抓取数据1.html"
    platform_file2 = "../../平台抓取数据2.html"
    test_file = "../../测试记录/对敲记录.json"

    try:
        results = analyzer.run_analysis(platform_file1, platform_file2, test_file)
        print("\n=== 分析完成 ===")

        # 生成详细报告
        report = analyzer.generate_report()
        print(report)

        # 保存报告到文件
        with open('wash_trading_analysis_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print("\n报告已保存到: wash_trading_analysis_report.txt")

        # 生成可视化图表
        try:
            analyzer.generate_visualizations()
            print("可视化图表已保存到 charts/ 目录")
        except Exception as viz_error:
            print(f"生成可视化图表时出现错误: {viz_error}")

        # 输出关键指标摘要
        print("\n=== 关键指标摘要 ===")
        algo_performance = results['algorithm_performance']
        print(f"检测率: {algo_performance['detection_rate_pct']:.2f}%")
        print(f"潜在检测率: {algo_performance['potential_detection_rate_pct']:.2f}%")
        print(f"精确匹配数量: {algo_performance['exact_matches']}/{algo_performance['total_test_records']}")

        if algo_performance.get('algorithm_compliance'):
            compliance = algo_performance['algorithm_compliance']
            print(f"算法符合度: {compliance['avg_overall_score']:.3f}")
            print(f"平均金额匹配分数: {compliance['avg_amount_match_score']:.3f}")
            print(f"平均时间匹配分数: {compliance['avg_primary_time_match_score']:.3f}")

        # 保存详细匹配结果到JSON
        import json
        match_details = {
            'summary': {
                'detection_rate_pct': algo_performance['detection_rate_pct'],
                'potential_detection_rate_pct': algo_performance['potential_detection_rate_pct'],
                'exact_matches': algo_performance['exact_matches'],
                'potential_matches': algo_performance['potential_matches'],
                'total_test_records': algo_performance['total_test_records']
            },
            'algorithm_compliance': algo_performance.get('algorithm_compliance', {}),
            'risk_analysis': algo_performance.get('risk_analysis', {}),
            'exact_matches': algo_performance['match_details']['exact_matches'],
            'potential_matches': algo_performance['match_details']['potential_matches']
        }

        with open('match_details.json', 'w', encoding='utf-8') as f:
            json.dump(match_details, f, ensure_ascii=False, indent=2, default=str)
        print("详细匹配结果已保存到: match_details.json")

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
