============================================================
对敲数据分析报告
============================================================
生成时间: 2025-08-04 15:03:42

1. 数据概览
------------------------------
平台数据记录数: 561
测试数据记录数: 2,292
时间范围重叠: 是

2. 金额分析
------------------------------
平台数据金额统计:
  平均值: 12,137.27 USDT
  中位数: 207.22 USDT
  标准差: 23,919.79 USDT
  范围: 7.93 - 95,647.79 USDT

测试数据金额统计:
  平均值: 12,354.37 USDT
  中位数: 1,218.35 USDT
  标准差: 23,021.06 USDT
  范围: 10.01 - 99,148.00 USDT

3. 交易对分析
------------------------------
平台数据交易对分布:
  DOGE-USDT: 239 条记录
  BTC-USDT: 184 条记录
  ETH-USDT: 138 条记录

测试数据交易对分布:
  BTC-USDT: 766 条记录
  DOGE-USDT: 764 条记录
  ETH-USDT: 762 条记录

4. 算法性能分析（基于订单ID精确匹配）
--------------------------------------------------
精确匹配率: 0.00%
部分匹配率: 0.00%
精确匹配数量: 0
部分匹配数量: 0
未匹配数量: 2,292
未匹配率: 100.00%

匹配方法说明:
  使用订单ID进行精确匹配:
  - 精确匹配: buy_order_id ↔ order_id 且 sell_order_id ↔ match_order_id
  - 部分匹配: 只有一个订单ID匹配
  - 盈亏对敲分数: 使用平台数据的真实盈亏计算

权重配置说明:
  盈亏对敲权重: 20% (使用真实盈亏数据)
  金额匹配权重: 35% (提高10%)
  平仓时间权重: 35% (提高10%)
  开仓时间权重: 10%

5. 结论和优化建议
----------------------------------------
基于检测率 0.00%，平台引擎检测性能严重不足，需要全面重构

优化建议:
  1. 全面审查算法逻辑
  2. 重新设计检测策略
  3. 增加更多匹配维度

关键发现:
• 使用订单ID进行精确匹配，避免了时间和金额的模糊匹配
• 平台数据包含真实的盈亏信息，可以计算准确的盈亏对敲分数
• 精确匹配能够准确识别测试数据在平台中的检测情况
• 高未匹配率(100.0%)表明平台可能未检测到大部分测试交易
