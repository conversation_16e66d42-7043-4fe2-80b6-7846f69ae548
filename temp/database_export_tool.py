#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库导出工具
支持多种格式和过滤条件的数据导出
"""

import sqlite3
import json
import csv
import os
from datetime import datetime
import pandas as pd

class DatabaseExporter:
    def __init__(self, db_path):
        """初始化数据库导出器"""
        self.db_path = db_path
        self.conn = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
    
    def get_table_info(self):
        """获取数据库表信息"""
        if not self.conn:
            return None
        
        cursor = self.conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        table_info = {}
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            table_info[table_name] = {
                'count': count,
                'columns': [col[1] for col in columns]
            }
        
        return table_info
    
    def export_table_to_csv(self, table_name, output_file, where_clause=None):
        """导出表到CSV文件"""
        if not self.conn:
            return False
        
        try:
            query = f"SELECT * FROM {table_name}"
            if where_clause:
                query += f" WHERE {where_clause}"
            
            cursor = self.conn.cursor()
            cursor.execute(query)
            
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                # 获取列名
                columns = [description[0] for description in cursor.description]
                writer = csv.writer(csvfile)
                writer.writerow(columns)
                
                # 写入数据
                rows_written = 0
                for row in cursor:
                    writer.writerow(row)
                    rows_written += 1
                
            print(f"✅ 成功导出 {rows_written} 行数据到 {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出CSV失败: {e}")
            return False
    
    def export_table_to_json(self, table_name, output_file, where_clause=None):
        """导出表到JSON文件"""
        if not self.conn:
            return False
        
        try:
            query = f"SELECT * FROM {table_name}"
            if where_clause:
                query += f" WHERE {where_clause}"
            
            cursor = self.conn.cursor()
            cursor.execute(query)
            
            # 获取所有数据
            rows = cursor.fetchall()
            
            # 转换为字典列表
            data = []
            for row in rows:
                data.append(dict(row))
            
            # 创建输出结构
            output_data = {
                "metadata": {
                    "exported_at": datetime.now().isoformat(),
                    "table_name": table_name,
                    "total_records": len(data),
                    "database_path": self.db_path,
                    "where_clause": where_clause
                },
                "data": data
            }
            
            # 写入JSON文件
            with open(output_file, 'w', encoding='utf-8') as jsonfile:
                json.dump(output_data, jsonfile, ensure_ascii=False, indent=2)
            
            print(f"✅ 成功导出 {len(data)} 行数据到 {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出JSON失败: {e}")
            return False
    
    def export_custom_query(self, query, output_file, format='csv'):
        """导出自定义查询结果"""
        if not self.conn:
            return False
        
        try:
            cursor = self.conn.cursor()
            cursor.execute(query)
            rows = cursor.fetchall()
            
            if format.lower() == 'csv':
                with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                    columns = [description[0] for description in cursor.description]
                    writer = csv.writer(csvfile)
                    writer.writerow(columns)
                    
                    for row in rows:
                        writer.writerow(row)
                        
            elif format.lower() == 'json':
                data = [dict(row) for row in rows]
                output_data = {
                    "metadata": {
                        "exported_at": datetime.now().isoformat(),
                        "query": query,
                        "total_records": len(data),
                        "database_path": self.db_path
                    },
                    "data": data
                }
                
                with open(output_file, 'w', encoding='utf-8') as jsonfile:
                    json.dump(output_data, jsonfile, ensure_ascii=False, indent=2)
            
            print(f"✅ 成功导出 {len(rows)} 行查询结果到 {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出查询结果失败: {e}")
            return False

def main():
    """主函数"""
    print("🗄️  数据库导出工具")
    print("=" * 50)
    
    # 查找数据库文件
    db_paths = [
        "wash_trading_test.db",
        "../wash_trading_test.db",
        "/Users/<USER>/Documents/augment-projects/web/wash_trading_test.db"
    ]
    
    selected_db = None
    for db_path in db_paths:
        if os.path.exists(db_path):
            selected_db = db_path
            break
    
    if not selected_db:
        print("❌ 未找到数据库文件")
        return
    
    print(f"📊 使用数据库: {selected_db}")
    
    # 初始化导出器
    exporter = DatabaseExporter(selected_db)
    if not exporter.connect():
        return
    
    try:
        # 获取表信息
        table_info = exporter.get_table_info()
        if not table_info:
            print("❌ 无法获取表信息")
            return
        
        print(f"\n📋 数据库表信息:")
        for table_name, info in table_info.items():
            print(f"   - {table_name}: {info['count']} 行, {len(info['columns'])} 列")
        
        while True:
            print(f"\n🔧 导出选项:")
            print("1. 导出完整表 (CSV)")
            print("2. 导出完整表 (JSON)")
            print("3. 按条件导出 (CSV)")
            print("4. 按条件导出 (JSON)")
            print("5. 自定义查询导出")
            print("6. 导出所有表")
            print("0. 退出")
            
            choice = input("\n请选择 (0-6): ").strip()
            
            if choice == '0':
                break
            elif choice in ['1', '2']:
                # 选择表
                print(f"\n可用的表:")
                table_list = list(table_info.keys())
                for i, table in enumerate(table_list, 1):
                    print(f"{i}. {table} ({table_info[table]['count']} 行)")
                
                try:
                    table_idx = int(input("选择表 (输入数字): ")) - 1
                    if 0 <= table_idx < len(table_list):
                        table_name = table_list[table_idx]
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        
                        if choice == '1':
                            output_file = f"{table_name}_{timestamp}.csv"
                            exporter.export_table_to_csv(table_name, output_file)
                        else:
                            output_file = f"{table_name}_{timestamp}.json"
                            exporter.export_table_to_json(table_name, output_file)
                    else:
                        print("❌ 无效的表选择")
                except ValueError:
                    print("❌ 请输入有效数字")
            
            elif choice in ['3', '4']:
                # 按条件导出
                print(f"\n可用的表:")
                table_list = list(table_info.keys())
                for i, table in enumerate(table_list, 1):
                    print(f"{i}. {table}")
                
                try:
                    table_idx = int(input("选择表 (输入数字): ")) - 1
                    if 0 <= table_idx < len(table_list):
                        table_name = table_list[table_idx]
                        where_clause = input("输入WHERE条件 (例: coin='BTC'): ").strip()
                        
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        
                        if choice == '3':
                            output_file = f"{table_name}_filtered_{timestamp}.csv"
                            exporter.export_table_to_csv(table_name, output_file, where_clause)
                        else:
                            output_file = f"{table_name}_filtered_{timestamp}.json"
                            exporter.export_table_to_json(table_name, output_file, where_clause)
                    else:
                        print("❌ 无效的表选择")
                except ValueError:
                    print("❌ 请输入有效数字")
            
            elif choice == '5':
                # 自定义查询
                query = input("输入SQL查询: ").strip()
                format_choice = input("选择格式 (csv/json): ").strip().lower()
                
                if format_choice in ['csv', 'json']:
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    output_file = f"custom_query_{timestamp}.{format_choice}"
                    exporter.export_custom_query(query, output_file, format_choice)
                else:
                    print("❌ 无效的格式选择")
            
            elif choice == '6':
                # 导出所有表
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                for table_name in table_info.keys():
                    csv_file = f"{table_name}_all_{timestamp}.csv"
                    json_file = f"{table_name}_all_{timestamp}.json"
                    exporter.export_table_to_csv(table_name, csv_file)
                    exporter.export_table_to_json(table_name, json_file)
                print("✅ 所有表导出完成")
            
            else:
                print("❌ 无效选择")
    
    finally:
        exporter.close()
        print("\n👋 导出工具已退出")

if __name__ == "__main__":
    main()
