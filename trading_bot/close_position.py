#!/usr/bin/env python3
"""
平单（平仓）功能
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient


def close_position(symbol="btc-usdt", orderQty=None, side=None):
    """
    平仓操作
    
    Args:
        symbol: 交易对
        orderQty: 平仓数量
        side: 平仓方向 (2=平多仓, 1=平空仓)
    """
    print(f"📉 平仓操作")
    print("=" * 40)
    print(f"交易对: {symbol}")
    print(f"数量: {orderQty}")
    print(f"方向: {'平多仓' if side == 2 else '平空仓' if side == 1 else '未知'}")
    print("=" * 40)
    
    try:
        # 初始化
        config = load_config('config.yaml')
        setup_logger(config)
        logger = get_logger('ClosePosition')
        
        client = BipcClient(config)
        
        # 平仓订单格式
        close_order = {
            "symbol": symbol,
            "orderQty": orderQty,
            "side": side,
            "type": "5",            # 关键：type=5 表示市价平仓
            "source": 1,
            "reduceOnly": 1,        # 关键：标识平仓操作
            "ppw": "W001"
        }
        
        print(f"平仓订单: {close_order}")
        logger.info(f"准备平仓: {close_order}")
        
        # 确认
        confirm = input("确认执行平仓操作？(YES/no): ").strip()
        if confirm != 'YES':
            print("❌ 用户取消")
            return None
        
        # 执行平仓
        url = client.endpoints.place_order
        result = client._make_request('POST', url, data=close_order, auth_required=True)
        
        print(f"平仓结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        logger.info(f"平仓结果: {result}")
        
        if result.get('code') == 200:
            print("✅ 平仓成功！")
            
            # 提取订单ID
            order_id = None
            if 'data' in result and isinstance(result['data'], dict):
                order_id = result['data'].get('orderId')
            
            print(f"📋 平仓订单ID: {order_id}")
            logger.info(f"平仓成功，订单ID: {order_id}")
            
            return {
                'success': True,
                'order_id': order_id,
                'order_data': close_order
            }
        else:
            print(f"❌ 平仓失败: {result.get('message')}")
            logger.error(f"平仓失败: {result}")
            return {
                'success': False,
                'error': result.get('message'),
                'order_data': close_order
            }
            
    except Exception as e:
        print(f"❌ 平仓异常: {e}")
        logger.error(f"平仓异常: {e}")
        return {
            'success': False,
            'error': str(e)
        }


def get_open_positions():
    """查询当前持仓"""
    print("📊 查询当前持仓")
    print("=" * 30)
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 查询持仓的可能端点
        position_endpoints = [
            '/swap/private/future/wallet/position',
            '/swap/private/future/position',
            '/swap/private/position',
            '/api/position'
        ]
        
        for endpoint in position_endpoints:
            try:
                url = f"{client.base_url}{endpoint}"
                result = client._make_request('GET', url, auth_required=True)
                
                print(f"✅ {endpoint} 可访问")
                print(f"持仓信息: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 解析持仓信息
                if result.get('code') == 200 and result.get('data'):
                    positions = result['data']
                    if isinstance(positions, list):
                        print(f"发现 {len(positions)} 个持仓")
                        for i, pos in enumerate(positions, 1):
                            print(f"持仓 {i}:")
                            print(f"  交易对: {pos.get('symbol')}")
                            print(f"  数量: {pos.get('volume', pos.get('size', 'N/A'))}")
                            print(f"  方向: {pos.get('side')}")
                            print(f"  价值: {pos.get('value', 'N/A')}")
                    elif isinstance(positions, dict) and 'pageData' in positions:
                        pos_list = positions['pageData']
                        print(f"发现 {len(pos_list)} 个持仓")
                        for i, pos in enumerate(pos_list, 1):
                            print(f"持仓 {i}:")
                            print(f"  交易对: {pos.get('symbol')}")
                            print(f"  数量: {pos.get('volume', pos.get('size', 'N/A'))}")
                            print(f"  方向: {pos.get('side')}")
                
                return result
                
            except Exception as e:
                print(f"❌ {endpoint} 失败: {str(e)[:100]}...")
        
        print("❌ 无法获取持仓信息")
        return None
        
    except Exception as e:
        print(f"❌ 查询持仓失败: {e}")
        return None


def close_all_positions():
    """平掉所有持仓"""
    print("🔄 平掉所有持仓")
    print("=" * 30)
    
    # 先查询持仓
    positions_result = get_open_positions()
    
    if not positions_result or positions_result.get('code') != 200:
        print("❌ 无法获取持仓信息")
        return
    
    positions_data = positions_result.get('data', [])
    if isinstance(positions_data, dict) and 'pageData' in positions_data:
        positions_list = positions_data['pageData']
    elif isinstance(positions_data, list):
        positions_list = positions_data
    else:
        positions_list = []
    
    if not positions_list:
        print("✅ 当前无持仓")
        return
    
    print(f"发现 {len(positions_list)} 个持仓，准备平仓...")
    
    confirm = input("确认平掉所有持仓？(YES/no): ").strip()
    if confirm != 'YES':
        print("❌ 用户取消")
        return
    
    # 逐个平仓
    for i, position in enumerate(positions_list, 1):
        symbol = position.get('symbol')
        volume = position.get('volume', position.get('size'))
        side = position.get('side')
        
        print(f"\n平仓 {i}/{len(positions_list)}: {symbol}")
        
        # 确定平仓方向（与持仓方向相反）
        if side == 1:  # 空仓，平仓用买入
            close_side = 2
        elif side == 2:  # 多仓，平仓用卖出
            close_side = 1
        else:
            print(f"❌ 未知持仓方向: {side}")
            continue
        
        # 执行平仓
        result = close_position(symbol, volume, close_side)
        
        if result and result['success']:
            print(f"✅ {symbol} 平仓成功")
        else:
            print(f"❌ {symbol} 平仓失败")
        
        # 等待一下避免频率限制
        if i < len(positions_list):
            time.sleep(2)


def main():
    """主函数"""
    print("📉 平单（平仓）工具")
    print("=" * 40)
    print("功能: 平仓操作，使用type=5和reduceOnly=1")
    print("=" * 40)
    
    while True:
        print("\n请选择:")
        print("1. 查询当前持仓")
        print("2. 手动平仓（指定参数）")
        print("3. 平掉所有持仓")
        print("4. 平仓BTC多仓（3000 USDT = 26 orderQty）")
        print("5. 平仓BTC空仓（3000 USDT = 26 orderQty）")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            get_open_positions()
        elif choice == '2':
            try:
                symbol = input("交易对 (默认btc-usdt): ").strip() or "btc-usdt"
                orderQty = int(input("平仓数量: "))
                side = int(input("平仓方向 (1=平空仓, 2=平多仓): "))
                close_position(symbol, orderQty, side)
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '3':
            close_all_positions()
        elif choice == '4':
            # 平BTC多仓 (3000 USDT对应orderQty=26)
            close_position("btc-usdt", 26, 1)  # side=1平多仓
        elif choice == '5':
            # 平BTC空仓 (3000 USDT对应orderQty=26)
            close_position("btc-usdt", 26, 2)  # side=2平空仓
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
