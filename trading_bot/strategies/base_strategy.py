"""
基础交易策略类
"""

import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

from utils.logger import get_logger


class BaseStrategy(ABC):
    """基础交易策略抽象类"""
    
    def __init__(self, client, config: Dict[str, Any]):
        """
        初始化策略
        
        Args:
            client: API客户端
            config: 配置字典
        """
        self.client = client
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.is_running = False
        self.start_time = None
        
        # 策略参数
        self.strategy_config = config.get('strategy', {})
        self.trading_config = config.get('trading', {})
        self.risk_config = config.get('risk', {})
        
        self.logger.info(f"初始化策略: {self.__class__.__name__}")
    
    @abstractmethod
    def analyze_market(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析市场数据
        
        Args:
            market_data: 市场数据
            
        Returns:
            分析结果
        """
        pass
    
    @abstractmethod
    def make_trading_decision(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        做出交易决策
        
        Args:
            analysis: 市场分析结果
            
        Returns:
            交易决策
        """
        pass
    
    @abstractmethod
    def execute_trade(self, decision: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行交易
        
        Args:
            decision: 交易决策
            
        Returns:
            交易结果
        """
        pass
    
    def start(self) -> None:
        """启动策略"""
        if self.is_running:
            self.logger.warning("策略已在运行中")
            return
        
        self.is_running = True
        self.start_time = datetime.now()
        self.logger.info("策略启动")
        
        try:
            self.run()
        except KeyboardInterrupt:
            self.logger.info("收到中断信号")
        except Exception as e:
            self.logger.error(f"策略运行错误: {e}")
            raise
        finally:
            self.stop()
    
    def stop(self) -> None:
        """停止策略"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.logger.info("策略停止")
    
    def run(self) -> None:
        """运行策略主循环"""
        check_interval = self.strategy_config.get('params', {}).get('check_interval', 10)
        symbol = self.trading_config.get('symbol', 'ETH-USDT')
        
        self.logger.info(f"开始监控 {symbol}，检查间隔: {check_interval}秒")
        
        while self.is_running:
            try:
                # 获取市场数据
                market_data = self.get_market_data(symbol)
                if not market_data:
                    self.logger.warning("无法获取市场数据")
                    time.sleep(check_interval)
                    continue
                
                # 分析市场
                analysis = self.analyze_market(market_data)
                
                # 做出交易决策
                decision = self.make_trading_decision(analysis)
                
                # 执行交易（如果需要）
                if decision.get('action') != 'hold':
                    result = self.execute_trade(decision)
                    self.logger.info(f"交易执行结果: {result}")
                
                # 等待下次检查
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"策略循环错误: {e}")
                time.sleep(check_interval)
    
    def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取市场数据
        
        Args:
            symbol: 交易对符号
            
        Returns:
            市场数据
        """
        try:
            # 获取ticker数据
            ticker_data = self.client.get_ticker(symbol)
            if ticker_data.get('success'):
                return ticker_data.get('data', {})
            else:
                self.logger.error(f"获取ticker失败: {ticker_data}")
                return None
        except Exception as e:
            self.logger.error(f"获取市场数据错误: {e}")
            return None
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息
        """
        return {
            'name': self.__class__.__name__,
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'config': self.strategy_config
        }
    
    def check_risk_limits(self, trade_amount: float) -> bool:
        """
        检查风险限制
        
        Args:
            trade_amount: 交易金额
            
        Returns:
            是否通过风险检查
        """
        max_order_value = self.risk_config.get('max_order_value', 50.0)
        
        if trade_amount > max_order_value:
            self.logger.warning(f"交易金额 {trade_amount} 超过最大限制 {max_order_value}")
            return False
        
        return True
