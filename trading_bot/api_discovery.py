#!/usr/bin/env python3
"""
API端点发现脚本
尝试发现BIPC交易所的真实API端点
"""

import requests
import json
import time
from typing import List, Dict, Any


class APIDiscovery:
    """API端点发现类"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
    
    def test_endpoint(self, path: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        测试API端点
        
        Args:
            path: API路径
            params: 请求参数
            
        Returns:
            测试结果
        """
        url = f"{self.base_url}{path}"
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            
            result = {
                'url': url,
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'content_type': response.headers.get('content-type', ''),
                'response_size': len(response.content)
            }
            
            # 尝试解析JSON响应
            try:
                if 'application/json' in result['content_type']:
                    result['json_data'] = response.json()
                else:
                    result['text_data'] = response.text[:500]  # 只保留前500字符
            except:
                result['text_data'] = response.text[:500]
            
            return result
            
        except Exception as e:
            return {
                'url': url,
                'status_code': 0,
                'success': False,
                'error': str(e)
            }
    
    def discover_api_endpoints(self) -> List[Dict[str, Any]]:
        """发现API端点"""
        
        # 常见的API路径模式
        api_patterns = [
            # 基础API路径
            '/api',
            '/api/v1',
            '/api/v2',
            '/api/v3',
            
            # 市场数据相关
            '/api/ticker',
            '/api/v1/ticker',
            '/api/v2/ticker',
            '/api/market/ticker',
            '/api/v1/market/ticker',
            '/api/public/ticker',
            '/api/v1/public/ticker',
            
            # 交易相关
            '/api/swap/ticker',
            '/api/v1/swap/ticker',
            '/api/contract/ticker',
            '/api/v1/contract/ticker',
            '/api/futures/ticker',
            '/api/v1/futures/ticker',
            
            # 深度数据
            '/api/depth',
            '/api/v1/depth',
            '/api/orderbook',
            '/api/v1/orderbook',
            '/api/market/depth',
            '/api/v1/market/depth',
            
            # K线数据
            '/api/klines',
            '/api/v1/klines',
            '/api/candles',
            '/api/v1/candles',
            '/api/market/klines',
            '/api/v1/market/klines',
            
            # 其他可能的路径
            '/open/api/v1/ticker',
            '/public/api/v1/ticker',
            '/fapi/v1/ticker/24hr',
            '/dapi/v1/ticker/24hr',
            '/sapi/v1/ticker/24hr',
        ]
        
        results = []
        
        print(f"开始探测API端点，基础URL: {self.base_url}")
        print("=" * 60)
        
        for i, pattern in enumerate(api_patterns, 1):
            print(f"[{i:2d}/{len(api_patterns)}] 测试: {pattern}")
            
            result = self.test_endpoint(pattern)
            results.append(result)
            
            if result['success']:
                print(f"  ✅ 成功! 状态码: {result['status_code']}")
                if 'json_data' in result:
                    print(f"  📄 JSON响应: {json.dumps(result['json_data'], ensure_ascii=False)[:100]}...")
            elif result['status_code'] == 404:
                print(f"  ❌ 404 Not Found")
            elif result['status_code'] == 0:
                print(f"  ⚠️  连接错误: {result.get('error', 'Unknown')}")
            else:
                print(f"  ⚠️  状态码: {result['status_code']}")
            
            # 避免请求过快
            time.sleep(0.5)
        
        return results
    
    def test_with_symbol(self, successful_endpoints: List[str], symbol: str = "ETHUSDT") -> None:
        """使用交易对符号测试成功的端点"""
        
        if not successful_endpoints:
            print("没有找到成功的端点，跳过符号测试")
            return
        
        print(f"\n使用交易对 {symbol} 测试成功的端点:")
        print("=" * 60)
        
        for endpoint in successful_endpoints:
            print(f"测试: {endpoint}?symbol={symbol}")
            
            result = self.test_endpoint(endpoint, {'symbol': symbol})
            
            if result['success']:
                print(f"  ✅ 成功!")
                if 'json_data' in result:
                    print(f"  📄 响应: {json.dumps(result['json_data'], ensure_ascii=False, indent=2)}")
            else:
                print(f"  ❌ 失败: {result['status_code']}")
            
            print()
            time.sleep(1)


def main():
    """主函数"""
    base_url = "https://new-bipc-9.bydtms.com"
    
    discovery = APIDiscovery(base_url)
    
    # 发现API端点
    results = discovery.discover_api_endpoints()
    
    # 找出成功的端点
    successful_endpoints = [
        result['url'].replace(base_url, '') 
        for result in results 
        if result['success']
    ]
    
    print(f"\n发现的成功端点:")
    print("=" * 60)
    for endpoint in successful_endpoints:
        print(f"  ✅ {endpoint}")
    
    if successful_endpoints:
        # 使用交易对测试
        discovery.test_with_symbol(successful_endpoints, "ETHUSDT")
        discovery.test_with_symbol(successful_endpoints, "ETH-USDT")
        discovery.test_with_symbol(successful_endpoints, "eth_usdt")
    else:
        print("  ❌ 没有发现可用的API端点")
    
    print("\nAPI发现完成!")


if __name__ == "__main__":
    main()
