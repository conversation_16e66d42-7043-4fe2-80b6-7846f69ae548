"""
配置管理模块
"""

import yaml
import os
from typing import Dict, Any


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 验证必要的配置项
    validate_config(config)
    
    return config


def validate_config(config: Dict[str, Any]) -> None:
    """
    验证配置文件的完整性
    
    Args:
        config: 配置字典
        
    Raises:
        ValueError: 配置项缺失或无效
    """
    required_sections = ['api', 'trading', 'strategy', 'risk', 'logging']
    
    for section in required_sections:
        if section not in config:
            raise ValueError(f"配置文件缺少必要的节: {section}")
    
    # 验证API配置
    api_config = config['api']
    required_api_fields = ['base_url', 'timeout', 'max_retries']
    for field in required_api_fields:
        if field not in api_config:
            raise ValueError(f"API配置缺少必要字段: {field}")
    
    # 验证交易配置
    trading_config = config['trading']
    required_trading_fields = ['symbol', 'min_quantity', 'max_quantity']
    for field in required_trading_fields:
        if field not in trading_config:
            raise ValueError(f"交易配置缺少必要字段: {field}")


def get_config_value(config: Dict[str, Any], key_path: str, default: Any = None) -> Any:
    """
    获取嵌套配置值
    
    Args:
        config: 配置字典
        key_path: 配置键路径，用点分隔，如 'api.base_url'
        default: 默认值
        
    Returns:
        配置值
    """
    keys = key_path.split('.')
    value = config
    
    try:
        for key in keys:
            value = value[key]
        return value
    except (KeyError, TypeError):
        return default
