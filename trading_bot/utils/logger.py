"""
日志管理模块
"""

import os
import sys
from loguru import logger
from typing import Dict, Any


def setup_logger(config: Dict[str, Any]) -> None:
    """
    设置日志系统
    
    Args:
        config: 配置字典
    """
    # 移除默认的日志处理器
    logger.remove()
    
    # 获取日志配置
    log_config = config.get('logging', {})
    log_level = log_config.get('level', 'INFO')
    log_file = log_config.get('file', 'logs/trading_bot.log')
    max_size = log_config.get('max_size', 10)
    backup_count = log_config.get('backup_count', 5)
    
    # 确保日志目录存在
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 控制台日志处理器
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 文件日志处理器
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=f"{max_size} MB",
        retention=backup_count,
        compression="zip",
        encoding="utf-8"
    )
    
    logger.info("日志系统初始化完成")


def get_logger(name: str = None):
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    if name:
        return logger.bind(name=name)
    return logger
