#!/usr/bin/env python3
"""
DOGE交易工具
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient


def usdt_to_doge_orderqty(usdt_amount: float) -> int:
    """USDT金额转换为DOGE orderQty"""
    # 基于真实数据：1 USDT = 4 orderQty
    return int(usdt_amount * 4)


def doge_orderqty_to_usdt(orderqty: int) -> float:
    """DOGE orderQty转换为USDT金额"""
    # 基于真实数据：1 USDT = 4 orderQty
    return orderqty / 4


def doge_trade_sequence(usdt_amount: float):
    """DOGE交易序列：买入→等待10秒→卖出"""
    print(f"🐕 DOGE交易序列 ({usdt_amount} USDT)")
    print("=" * 50)
    print("流程: 买入DOGE → 等待10秒 → 卖出DOGE")
    print("=" * 50)
    
    try:
        # 初始化
        config = load_config('config.yaml')
        setup_logger(config)
        logger = get_logger('DOGETrader')
        
        client = BipcClient(config)
        
        # 计算DOGE数量
        doge_orderQty = usdt_to_doge_orderqty(usdt_amount)
        
        print(f"交易参数:")
        print(f"- USDT金额: {usdt_amount}")
        print(f"- DOGE orderQty: {doge_orderQty}")
        print(f"- 换算比例: 1 USDT = 4 orderQty")
        
        # 确认
        confirm = input(f"\n确认执行DOGE交易序列？(YES/no): ").strip()
        if confirm != 'YES':
            print("❌ 用户取消")
            return
        
        # 买入DOGE
        buy_order = {
            "symbol": "doge-usdt",
            "orderQty": doge_orderQty,
            "side": 2,              # 买入
            "type": "2",            # 市价单
            "source": 1,
            "ppw": "W001"
        }
        
        print(f"\n🐕 买入DOGE...")
        print(f"订单: {buy_order}")
        logger.info(f"买入DOGE订单: {buy_order}")
        
        # 执行买入
        url = client.endpoints.place_order
        buy_result = client._make_request('POST', url, data=buy_order, auth_required=True)
        
        print(f"买入结果: {json.dumps(buy_result, indent=2, ensure_ascii=False)}")
        logger.info(f"买入结果: {buy_result}")
        
        if buy_result.get('code') == 200:
            print("✅ 买入DOGE成功！")
            
            # 提取订单ID
            buy_order_id = None
            if 'data' in buy_result and isinstance(buy_result['data'], dict):
                buy_order_id = buy_result['data'].get('orderId')
            
            print(f"📋 买入订单ID: {buy_order_id}")
            
            # 等待10秒
            print(f"\n⏰ 等待10秒...")
            for i in range(10, 0, -1):
                print(f"   倒计时: {i} 秒", end='\r')
                time.sleep(1)
            print("\n")
            
            # 卖出DOGE
            sell_order = {
                "symbol": "doge-usdt",
                "orderQty": doge_orderQty,
                "side": 1,              # 卖出
                "type": "2",            # 市价单
                "source": 1,
                "ppw": "W001"
            }
            
            print(f"🐕 卖出DOGE...")
            print(f"订单: {sell_order}")
            logger.info(f"卖出DOGE订单: {sell_order}")
            
            # 执行卖出
            sell_result = client._make_request('POST', url, data=sell_order, auth_required=True)
            
            print(f"卖出结果: {json.dumps(sell_result, indent=2, ensure_ascii=False)}")
            logger.info(f"卖出结果: {sell_result}")
            
            if sell_result.get('code') == 200:
                print("✅ 卖出DOGE成功！")
                
                # 提取订单ID
                sell_order_id = None
                if 'data' in sell_result and isinstance(sell_result['data'], dict):
                    sell_order_id = sell_result['data'].get('orderId')
                
                print(f"📋 卖出订单ID: {sell_order_id}")
                
                # 交易完成
                print(f"\n🎉 DOGE交易序列完成！")
                print("=" * 40)
                print(f"买入订单ID: {buy_order_id}")
                print(f"卖出订单ID: {sell_order_id}")
                print(f"交易orderQty: {doge_orderQty}")
                print(f"交易金额: {usdt_amount} USDT")
                print(f"总耗时: 约10秒")
                
                logger.info(f"DOGE交易完成: 买入{buy_order_id}, 卖出{sell_order_id}")
                
            else:
                print(f"❌ 卖出DOGE失败: {sell_result.get('message')}")
                logger.error(f"卖出失败: {sell_result}")
                
        else:
            print(f"❌ 买入DOGE失败: {buy_result.get('message')}")
            logger.error(f"买入失败: {buy_result}")
            
    except Exception as e:
        print(f"❌ DOGE交易失败: {e}")
        logger.error(f"DOGE交易失败: {e}")


def doge_close_position(orderqty: int, side: int):
    """DOGE平仓操作"""
    print(f"📉 DOGE平仓操作")
    print("=" * 30)
    print(f"数量: {orderqty} orderQty")
    print(f"方向: {'平多仓' if side == 1 else '平空仓' if side == 2 else '未知'}")
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 平仓订单
        close_order = {
            "symbol": "doge-usdt",
            "orderQty": orderqty,
            "side": side,
            "type": "5",            # 平仓
            "source": 1,
            "reduceOnly": 1,        # 平仓标识
            "ppw": "W001"
        }
        
        print(f"平仓订单: {close_order}")
        
        confirm = input("确认执行DOGE平仓？(YES/no): ").strip()
        if confirm != 'YES':
            print("❌ 用户取消")
            return
        
        # 执行平仓
        url = client.endpoints.place_order
        result = client._make_request('POST', url, data=close_order, auth_required=True)
        
        print(f"平仓结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('code') == 200:
            print("✅ DOGE平仓成功！")
            order_id = None
            if 'data' in result and isinstance(result['data'], dict):
                order_id = result['data'].get('orderId')
            print(f"📋 平仓订单ID: {order_id}")
        else:
            print(f"❌ DOGE平仓失败: {result.get('message')}")
            
    except Exception as e:
        print(f"❌ DOGE平仓异常: {e}")


def show_doge_conversion_table():
    """显示DOGE换算表"""
    print("📊 DOGE-USDT 数量换算表")
    print("=" * 40)
    print("基于真实数据: 1 USDT = 4 orderQty")
    print("=" * 40)
    
    # 常用USDT金额
    usdt_amounts = [0.25, 0.5, 1, 5, 10, 25, 50, 100, 500, 1000]
    
    print("USDT金额 → orderQty")
    print("-" * 25)
    for usdt in usdt_amounts:
        orderqty = usdt_to_doge_orderqty(usdt)
        print(f"{usdt:>8} USDT → {orderqty:>4} orderQty")
    
    print("\norderQty → USDT金额")
    print("-" * 25)
    orderqty_amounts = [1, 4, 20, 40, 100, 400, 2000, 4000]
    for orderqty in orderqty_amounts:
        usdt = doge_orderqty_to_usdt(orderqty)
        print(f"{orderqty:>4} orderQty → {usdt:>8} USDT")


def main():
    """主函数"""
    print("🐕 DOGE交易工具")
    print("=" * 40)
    print("基于真实数据: 1 USDT = 4 orderQty")
    print("=" * 40)
    
    while True:
        print("\n请选择:")
        print("1. DOGE交易序列（自定义金额）")
        print("2. 快速DOGE交易（1 USDT）")
        print("3. 快速DOGE交易（10 USDT）")
        print("4. 快速DOGE交易（100 USDT）")
        print("5. DOGE平仓（平多仓）")
        print("6. DOGE平仓（平空仓）")
        print("7. 显示DOGE换算表")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-7): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            try:
                amount = float(input("请输入USDT金额: "))
                if amount > 0:
                    doge_trade_sequence(amount)
                else:
                    print("❌ 金额必须大于0")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '2':
            doge_trade_sequence(1)
        elif choice == '3':
            doge_trade_sequence(10)
        elif choice == '4':
            doge_trade_sequence(100)
        elif choice == '5':
            try:
                orderqty = int(input("请输入平仓数量(orderQty): "))
                doge_close_position(orderqty, 1)  # 平多仓
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '6':
            try:
                orderqty = int(input("请输入平仓数量(orderQty): "))
                doge_close_position(orderqty, 2)  # 平空仓
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '7':
            show_doge_conversion_table()
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
