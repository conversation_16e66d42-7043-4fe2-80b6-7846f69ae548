#!/usr/bin/env python3
"""
快速BTC交易 - 无等待间隔
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient


def quick_btc_trade(usdt_amount=3000):
    """快速BTC交易序列 - 无30秒等待"""
    print(f"⚡ 快速BTC交易序列 ({usdt_amount} USDT)")
    print("=" * 50)
    print("流程: 买入 → 等待10秒 → 卖出")
    print("=" * 50)
    
    try:
        # 初始化
        config = load_config('config.yaml')
        setup_logger(config)
        logger = get_logger('QuickBTC')
        
        client = BipcClient(config)
        
        # 计算BTC数量 (基于真实换算: 10000 USDT = 86 orderQty)
        btc_orderQty = int(usdt_amount * 86 / 10000)
        
        print(f"交易参数:")
        print(f"- USDT金额: {usdt_amount}")
        print(f"- orderQty: {btc_orderQty} (换算: {usdt_amount} USDT × 86/10000)")
        print(f"- 换算比例: 10000 USDT = 86 orderQty")
        
        # 确认
        confirm = input(f"\n确认执行快速BTC交易？(YES/no): ").strip()
        if confirm != 'YES':
            print("❌ 用户取消")
            return
        
        # 买入订单
        buy_order = {
            "symbol": "btc-usdt",
            "orderQty": btc_orderQty,
            "side": 2,              # 买入
            "type": "2",            # 市价单
            "source": 1,
            "ppw": "W001"
        }
        
        print(f"\n📈 买入BTC...")
        print(f"订单: {buy_order}")
        logger.info(f"买入订单: {buy_order}")
        
        # 执行买入
        url = client.endpoints.place_order
        buy_result = client._make_request('POST', url, data=buy_order, auth_required=True)
        
        print(f"买入结果: {json.dumps(buy_result, indent=2, ensure_ascii=False)}")
        logger.info(f"买入结果: {buy_result}")
        
        if buy_result.get('code') == 200:
            print("✅ 买入成功！")
            
            # 提取订单ID
            buy_order_id = None
            if 'data' in buy_result and isinstance(buy_result['data'], dict):
                buy_order_id = buy_result['data'].get('orderId')
            
            print(f"📋 买入订单ID: {buy_order_id}")
            
            # 等待10秒
            print(f"\n⏰ 等待10秒...")
            for i in range(10, 0, -1):
                print(f"   倒计时: {i} 秒", end='\r')
                time.sleep(1)
            print("\n")
            
            # 卖出订单
            sell_order = {
                "symbol": "btc-usdt",
                "orderQty": btc_orderQty,
                "side": 1,              # 卖出
                "type": "2",            # 市价单
                "source": 1,
                "ppw": "W001"
            }
            
            print(f"📉 卖出BTC...")
            print(f"订单: {sell_order}")
            logger.info(f"卖出订单: {sell_order}")
            
            # 执行卖出
            sell_result = client._make_request('POST', url, data=sell_order, auth_required=True)
            
            print(f"卖出结果: {json.dumps(sell_result, indent=2, ensure_ascii=False)}")
            logger.info(f"卖出结果: {sell_result}")
            
            if sell_result.get('code') == 200:
                print("✅ 卖出成功！")
                
                # 提取订单ID
                sell_order_id = None
                if 'data' in sell_result and isinstance(sell_result['data'], dict):
                    sell_order_id = sell_result['data'].get('orderId')
                
                print(f"📋 卖出订单ID: {sell_order_id}")
                
                # 交易完成
                print(f"\n🎉 快速BTC交易完成！")
                print("=" * 40)
                print(f"买入订单ID: {buy_order_id}")
                print(f"卖出订单ID: {sell_order_id}")
                print(f"交易orderQty: {btc_orderQty}")
                print(f"交易金额: {usdt_amount} USDT")
                print(f"总耗时: 约10秒")
                
                logger.info(f"快速BTC交易完成: 买入{buy_order_id}, 卖出{sell_order_id}")
                
            else:
                print(f"❌ 卖出失败: {sell_result.get('message')}")
                logger.error(f"卖出失败: {sell_result}")
                
        else:
            print(f"❌ 买入失败: {buy_result.get('message')}")
            logger.error(f"买入失败: {buy_result}")
            
    except Exception as e:
        print(f"❌ 快速交易失败: {e}")
        logger.error(f"快速交易失败: {e}")


def main():
    """主函数"""
    print("⚡ 快速BTC交易工具")
    print("=" * 40)
    print("特点: 无30秒等待，立即执行")
    print("=" * 40)
    
    while True:
        print("\n请选择:")
        print("1. 快速3000 USDT交易")
        print("2. 快速1000 USDT交易")
        print("3. 快速500 USDT交易")
        print("4. 自定义金额交易")
        print("5. 查询当前订单")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            quick_btc_trade(3000)
        elif choice == '2':
            quick_btc_trade(1000)
        elif choice == '3':
            quick_btc_trade(500)
        elif choice == '4':
            try:
                amount = float(input("请输入USDT金额: "))
                if amount > 0:
                    quick_btc_trade(amount)
                else:
                    print("❌ 金额必须大于0")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '5':
            try:
                config = load_config('config.yaml')
                client = BipcClient(config)
                orders = client.get_open_orders()
                print("\n当前开放订单:")
                print(json.dumps(orders, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
