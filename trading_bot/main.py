"""
自动交易机器人主程序
"""

import sys
import os
import time
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient
from strategies.simple_strategy import SimpleStrategy


def main():
    """主程序入口"""
    try:
        # 加载配置
        config = load_config('config.yaml')
        
        # 设置日志
        setup_logger(config)
        logger = get_logger('Main')
        
        logger.info("=== BIPC自动交易机器人启动 ===")
        
        # 初始化API客户端
        client = BipcClient(config)
        
        # 获取交易配置
        trading_config = config['trading']
        symbol = trading_config['symbol']
        
        logger.info(f"开始监控交易对: {symbol}")
        
        # 测试API连接
        test_api_connection(client, symbol)
        
        # 启动交易策略
        run_trading_strategy(client, config)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止程序...")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        raise
    finally:
        logger.info("=== 程序已停止 ===")


def test_api_connection(client: BipcClient, symbol: str) -> None:
    """
    测试API连接
    
    Args:
        client: API客户端
        symbol: 交易对符号
    """
    logger = get_logger('APITest')
    
    try:
        logger.info("测试API连接...")
        
        # 测试获取ticker数据
        ticker_data = client.get_ticker(symbol)
        logger.info(f"Ticker数据获取成功: {ticker_data}")
        
        # 测试获取深度数据
        depth_data = client.get_depth(symbol, limit=5)
        logger.info(f"深度数据获取成功: {depth_data}")
        
        # 测试获取K线数据
        klines_data = client.get_klines(symbol, interval='1m', limit=5)
        logger.info(f"K线数据获取成功: {klines_data}")
        
        logger.info("API连接测试完成")
        
    except Exception as e:
        logger.error(f"API连接测试失败: {e}")
        logger.warning("请检查网络连接和API配置")
        # 不抛出异常，允许程序继续运行


def run_trading_strategy(client: BipcClient, config: Dict[str, Any]) -> None:
    """
    运行交易策略

    Args:
        client: API客户端
        config: 配置字典
    """
    logger = get_logger('TradingStrategy')

    strategy_type = config['strategy'].get('type', 'simple')

    logger.info(f"启动交易策略: {strategy_type}")

    try:
        # 根据配置选择策略
        if strategy_type == 'simple':
            strategy = SimpleStrategy(client, config)
        else:
            logger.error(f"未知的策略类型: {strategy_type}")
            return

        # 显示策略信息
        strategy_info = strategy.get_strategy_info()
        logger.info(f"策略信息: {strategy_info}")

        # 启动策略
        strategy.start()

    except Exception as e:
        logger.error(f"策略运行失败: {e}")
        raise


def analyze_market_and_trade(client: BipcClient, config: Dict[str, Any], market_data: Dict[str, Any]) -> None:
    """
    分析市场并执行交易
    
    Args:
        client: API客户端
        config: 配置字典
        market_data: 市场数据
    """
    logger = get_logger('Strategy')
    
    # 这里将来会实现具体的交易策略
    logger.info("交易策略分析中...")
    
    # 示例：简单的价格监控
    current_price = float(market_data.get('price', 0))
    logger.info(f"当前价格: {current_price}")
    
    # TODO: 实现具体的交易逻辑


if __name__ == "__main__":
    main()
