#!/usr/bin/env python3
"""
BTC 3000 USDT交易序列
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient


def execute_btc_3000_trading():
    """执行BTC 3000 USDT交易序列"""
    print("🚀 BTC 3000 USDT交易序列")
    print("=" * 50)
    print("计划:")
    print("1. 买入约3000 USDT的BTC")
    print("2. 等待10秒")
    print("3. 卖出相同数量的BTC")
    print("=" * 50)
    
    try:
        # 初始化
        config = load_config('config.yaml')
        setup_logger(config)
        logger = get_logger('BTC3000')
        
        client = BipcClient(config)
        
        # 计算BTC数量
        estimated_btc_price = 95000.0  # BTC估算价格
        usdt_amount = 3000.0
        btc_orderQty = int(usdt_amount / estimated_btc_price * 100000)  # 转换为orderQty单位
        
        print(f"交易参数:")
        print(f"- USDT金额: {usdt_amount}")
        print(f"- 估算BTC价格: {estimated_btc_price}")
        print(f"- 计算orderQty: {btc_orderQty}")
        
        # 最终确认
        confirm = input(f"\n确认执行BTC 3000 USDT交易序列？(YES/no): ").strip()
        if confirm != 'YES':
            print("❌ 用户取消")
            return
        
        # 直接开始交易，不等待
        
        # 第一步：买入BTC
        print("📈 第一步：买入BTC")
        print("-" * 40)
        
        buy_order_data = {
            "symbol": "btc-usdt",
            "orderQty": btc_orderQty,
            "side": 2,              # 2=买入
            "type": "2",            # 市价单
            "source": 1,
            "ppw": "W001"           # 子钱包参数
        }
        
        print(f"买入订单: {buy_order_data}")
        logger.info(f"准备买入BTC: {buy_order_data}")
        
        # 直接调用API
        url = client.endpoints.place_order
        buy_result = client._make_request('POST', url, data=buy_order_data, auth_required=True)
        
        print(f"买入结果: {json.dumps(buy_result, indent=2, ensure_ascii=False)}")
        logger.info(f"买入结果: {buy_result}")
        
        # 检查买入结果
        if buy_result.get('code') == 200:
            print("✅ 买入订单提交成功！")
            
            # 提取订单ID
            buy_order_id = None
            if 'data' in buy_result and isinstance(buy_result['data'], dict):
                buy_order_id = buy_result['data'].get('orderId')
            else:
                buy_order_id = buy_result.get('orderId')
            
            print(f"📋 买入订单ID: {buy_order_id}")
            
            # 第二步：等待10秒
            print(f"\n⏰ 等待10秒...")
            for i in range(10, 0, -1):
                print(f"   倒计时: {i} 秒", end='\r')
                time.sleep(1)
            print("\n")
            
            # 第三步：卖出BTC
            print("📉 第三步：卖出BTC")
            print("-" * 40)
            
            sell_order_data = {
                "symbol": "btc-usdt",
                "orderQty": btc_orderQty,
                "side": 1,              # 1=卖出
                "type": "2",            # 市价单
                "source": 1,
                "ppw": "W001"           # 子钱包参数
            }
            
            print(f"卖出订单: {sell_order_data}")
            logger.info(f"准备卖出BTC: {sell_order_data}")
            
            # 执行卖出
            sell_result = client._make_request('POST', url, data=sell_order_data, auth_required=True)
            
            print(f"卖出结果: {json.dumps(sell_result, indent=2, ensure_ascii=False)}")
            logger.info(f"卖出结果: {sell_result}")
            
            # 检查卖出结果
            if sell_result.get('code') == 200:
                print("✅ 卖出订单提交成功！")
                
                # 提取订单ID
                sell_order_id = None
                if 'data' in sell_result and isinstance(sell_result['data'], dict):
                    sell_order_id = sell_result['data'].get('orderId')
                else:
                    sell_order_id = sell_result.get('orderId')
                
                print(f"📋 卖出订单ID: {sell_order_id}")
                
                # 交易完成总结
                print("\n🎉 BTC 3000 USDT交易序列完成！")
                print("=" * 50)
                print(f"买入订单ID: {buy_order_id}")
                print(f"卖出订单ID: {sell_order_id}")
                print(f"交易orderQty: {btc_orderQty}")
                print(f"交易金额: 约{usdt_amount} USDT")
                print(f"交易对: btc-usdt")
                
                # 查询最终状态
                print("\n🔍 查询最终订单状态...")
                try:
                    orders = client.get_open_orders()
                    open_count = len(orders.get('data', {}).get('pageData', []))
                    print(f"当前开放订单数量: {open_count}")
                except Exception as e:
                    print(f"查询订单状态失败: {e}")
                
                logger.info("BTC 3000 USDT交易序列完成")
                
            else:
                print(f"❌ 卖出失败: {sell_result.get('message', '未知错误')}")
                logger.error(f"卖出失败: {sell_result}")
                
        else:
            print(f"❌ 买入失败: {buy_result.get('message', '未知错误')}")
            logger.error(f"买入失败: {buy_result}")
            
    except Exception as e:
        print(f"❌ 交易序列执行失败: {e}")
        logger.error(f"交易序列失败: {e}")


def test_small_btc_order():
    """测试小额BTC订单"""
    print("🧪 测试小额BTC订单")
    print("=" * 30)
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 小额测试订单
        test_order = {
            "symbol": "btc-usdt",
            "orderQty": 1,
            "side": 2,              # 买入
            "type": "2",            # 市价单
            "source": 1,
            "ppw": "W001"           # 子钱包参数
        }
        
        print(f"测试订单: {test_order}")
        
        confirm = input("确认发送小额测试订单？(yes/NO): ").strip()
        if confirm.lower() != 'yes':
            print("❌ 用户取消")
            return
        
        # 发送订单
        url = client.endpoints.place_order
        result = client._make_request('POST', url, data=test_order, auth_required=True)
        
        print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('code') == 200:
            print("✅ 小额测试成功！")
            
            # 正确提取订单ID
            order_id = None
            if 'data' in result and isinstance(result['data'], dict):
                order_id = result['data'].get('orderId')
            else:
                order_id = result.get('orderId')
                
            print(f"📋 订单ID: {order_id}")
            return True
        else:
            print(f"❌ 测试失败: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


def main():
    """主函数"""
    print("🎯 BTC 3000 USDT交易测试")
    print("=" * 50)
    print("使用正确的API格式进行BTC交易")
    print("=" * 50)
    
    while True:
        print("\n请选择:")
        print("1. 小额BTC测试")
        print("2. 完整3000 USDT交易序列")
        print("3. 查询当前订单")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            if test_small_btc_order():
                print("✅ 小额测试成功，可以进行完整交易")
        elif choice == '2':
            execute_btc_3000_trading()
        elif choice == '3':
            try:
                config = load_config('config.yaml')
                client = BipcClient(config)
                orders = client.get_open_orders()
                print("\n当前开放订单:")
                print(json.dumps(orders, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
