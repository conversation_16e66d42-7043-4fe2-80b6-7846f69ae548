#!/usr/bin/env python3
"""
数量换算工具
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from api.client import BipcClient


class QuantityCalculator:
    """数量换算器"""
    
    def __init__(self):
        # 基于真实数据的换算比例
        self.conversion_rates = {
            'btc-usdt': {
                'usdt_per_orderqty': 10000 / 86,  # 10000 USDT = 86 orderQty
                'orderqty_per_usdt': 86 / 10000   # 1 USDT = 0.0086 orderQty
            },
            'doge-usdt': {
                'usdt_per_orderqty': 1 / 4,       # 1 USDT = 4 orderQty
                'orderqty_per_usdt': 4 / 1        # 1 USDT = 4 orderQty
            }
        }
    
    def usdt_to_orderqty(self, symbol: str, usdt_amount: float) -> int:
        """USDT金额转换为orderQty"""
        if symbol not in self.conversion_rates:
            raise ValueError(f"不支持的交易对: {symbol}")
        
        rate = self.conversion_rates[symbol]['orderqty_per_usdt']
        orderqty = int(usdt_amount * rate)
        return orderqty
    
    def orderqty_to_usdt(self, symbol: str, orderqty: int) -> float:
        """orderQty转换为USDT金额"""
        if symbol not in self.conversion_rates:
            raise ValueError(f"不支持的交易对: {symbol}")
        
        rate = self.conversion_rates[symbol]['usdt_per_orderqty']
        usdt_amount = orderqty * rate
        return round(usdt_amount, 2)
    
    def show_conversion_table(self, symbol: str = 'btc-usdt'):
        """显示换算表"""
        print(f"📊 {symbol.upper()} 数量换算表")
        print("=" * 40)
        
        # 常用USDT金额
        usdt_amounts = [100, 500, 1000, 3000, 5000, 10000, 20000, 50000]
        
        print("USDT金额 → orderQty")
        print("-" * 25)
        for usdt in usdt_amounts:
            orderqty = self.usdt_to_orderqty(symbol, usdt)
            print(f"{usdt:>8} USDT → {orderqty:>3} orderQty")
        
        print("\norderQty → USDT金额")
        print("-" * 25)
        orderqty_amounts = [1, 5, 10, 26, 43, 86, 172, 430]
        for orderqty in orderqty_amounts:
            usdt = self.orderqty_to_usdt(symbol, orderqty)
            print(f"{orderqty:>3} orderQty → {usdt:>8} USDT")


def test_conversion():
    """测试换算功能"""
    print("🧪 测试数量换算")
    print("=" * 30)

    calc = QuantityCalculator()

    # 测试BTC数据
    print("BTC-USDT 测试:")
    btc_test_cases = [
        (10000, 86),  # 已知：10000 USDT = 86 orderQty
        (3000, 26),   # 计算：3000 USDT = 26 orderQty
        (1000, 9),    # 计算：1000 USDT = 9 orderQty
        (500, 4),     # 计算：500 USDT = 4 orderQty
    ]

    for usdt, expected_orderqty in btc_test_cases:
        calculated = calc.usdt_to_orderqty('btc-usdt', usdt)
        status = "✅" if calculated == expected_orderqty else "❌"
        print(f"{status} {usdt} USDT → {calculated} orderQty (期望: {expected_orderqty})")

    # 测试DOGE数据
    print("\nDOGE-USDT 测试:")
    doge_test_cases = [
        (1, 4),     # 已知：1 USDT = 4 orderQty
        (10, 40),   # 计算：10 USDT = 40 orderQty
        (100, 400), # 计算：100 USDT = 400 orderQty
        (0.25, 1),  # 计算：0.25 USDT = 1 orderQty
    ]

    for usdt, expected_orderqty in doge_test_cases:
        calculated = calc.usdt_to_orderqty('doge-usdt', usdt)
        status = "✅" if calculated == expected_orderqty else "❌"
        print(f"{status} {usdt} USDT → {calculated} orderQty (期望: {expected_orderqty})")


def interactive_calculator():
    """交互式计算器"""
    print("🧮 交互式数量计算器")
    print("=" * 40)
    
    calc = QuantityCalculator()
    
    while True:
        print("\n请选择:")
        print("1. USDT → orderQty")
        print("2. orderQty → USDT")
        print("3. 显示换算表")
        print("4. 测试换算功能")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            try:
                usdt = float(input("请输入USDT金额: "))
                orderqty = calc.usdt_to_orderqty('btc-usdt', usdt)
                print(f"结果: {usdt} USDT = {orderqty} orderQty")
            except ValueError as e:
                print(f"❌ 错误: {e}")
        elif choice == '2':
            try:
                orderqty = int(input("请输入orderQty: "))
                usdt = calc.orderqty_to_usdt('btc-usdt', orderqty)
                print(f"结果: {orderqty} orderQty = {usdt} USDT")
            except ValueError as e:
                print(f"❌ 错误: {e}")
        elif choice == '3':
            calc.show_conversion_table()
        elif choice == '4':
            test_conversion()
        else:
            print("❌ 无效选择")


def create_trading_with_usdt():
    """基于USDT金额的交易功能"""
    print("💰 基于USDT金额的BTC交易")
    print("=" * 40)
    
    calc = QuantityCalculator()
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 输入USDT金额
        usdt_amount = float(input("请输入交易金额(USDT): "))
        orderqty = calc.usdt_to_orderqty('btc-usdt', usdt_amount)
        
        print(f"\n交易参数:")
        print(f"- USDT金额: {usdt_amount}")
        print(f"- orderQty: {orderqty}")
        print(f"- 换算比例: 10000 USDT = 86 orderQty")
        
        # 选择交易类型
        print("\n交易类型:")
        print("1. 开多仓 (买入)")
        print("2. 开空仓 (卖出)")
        print("3. 平多仓")
        print("4. 平空仓")
        
        trade_type = input("请选择交易类型 (1-4): ").strip()
        
        if trade_type == '1':
            # 开多仓
            order = {
                "symbol": "btc-usdt",
                "orderQty": orderqty,
                "side": 2,
                "type": "2",
                "source": 1,
                "ppw": "W001"
            }
            action = "开多仓"
        elif trade_type == '2':
            # 开空仓
            order = {
                "symbol": "btc-usdt",
                "orderQty": orderqty,
                "side": 1,
                "type": "2",
                "source": 1,
                "ppw": "W001"
            }
            action = "开空仓"
        elif trade_type == '3':
            # 平多仓
            order = {
                "symbol": "btc-usdt",
                "orderQty": orderqty,
                "side": 1,
                "type": "5",
                "source": 1,
                "reduceOnly": 1,
                "ppw": "W001"
            }
            action = "平多仓"
        elif trade_type == '4':
            # 平空仓
            order = {
                "symbol": "btc-usdt",
                "orderQty": orderqty,
                "side": 2,
                "type": "5",
                "source": 1,
                "reduceOnly": 1,
                "ppw": "W001"
            }
            action = "平空仓"
        else:
            print("❌ 无效选择")
            return
        
        print(f"\n{action}订单: {order}")
        
        confirm = input(f"确认执行{action}？(YES/no): ").strip()
        if confirm != 'YES':
            print("❌ 用户取消")
            return
        
        # 执行交易
        url = client.endpoints.place_order
        result = client._make_request('POST', url, data=order, auth_required=True)
        
        print(f"{action}结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('code') == 200:
            print(f"✅ {action}成功！")
            order_id = None
            if 'data' in result and isinstance(result['data'], dict):
                order_id = result['data'].get('orderId')
            print(f"📋 订单ID: {order_id}")
        else:
            print(f"❌ {action}失败: {result.get('message')}")
            
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 交易失败: {e}")


def main():
    """主函数"""
    print("📊 BTC数量换算工具")
    print("=" * 40)
    print("基于真实数据: 10000 USDT = 86 orderQty")
    print("=" * 40)
    
    while True:
        print("\n请选择功能:")
        print("1. 交互式计算器")
        print("2. 显示换算表")
        print("3. 测试换算功能")
        print("4. 基于USDT金额交易")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            interactive_calculator()
        elif choice == '2':
            calc = QuantityCalculator()
            calc.show_conversion_table()
        elif choice == '3':
            test_conversion()
        elif choice == '4':
            create_trading_with_usdt()
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
