"""
API测试脚本
用于测试和调试API接口
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient


def main():
    """测试主函数"""
    try:
        # 加载配置
        config = load_config('config.yaml')
        
        # 设置日志
        setup_logger(config)
        logger = get_logger('APITest')
        
        logger.info("=== API接口测试开始 ===")
        
        # 初始化API客户端
        client = BipcClient(config)
        
        # 获取交易对
        symbol = config['trading']['symbol']
        
        # 测试各种API接口
        test_ticker(client, symbol)
        test_depth(client, symbol)
        test_klines(client, symbol)
        
        logger.info("=== API接口测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        raise


def test_ticker(client: BipcClient, symbol: str):
    """测试ticker接口"""
    logger = get_logger('TickerTest')
    
    try:
        logger.info(f"测试获取{symbol}的ticker数据...")
        
        result = client.get_ticker(symbol)
        
        logger.info("Ticker数据获取成功:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 解析关键数据
        if 'price' in result:
            logger.info(f"当前价格: {result['price']}")
        if 'volume' in result:
            logger.info(f"24h成交量: {result['volume']}")
        if 'change' in result:
            logger.info(f"24h涨跌幅: {result['change']}")
            
    except Exception as e:
        logger.error(f"Ticker测试失败: {e}")


def test_depth(client: BipcClient, symbol: str):
    """测试深度接口"""
    logger = get_logger('DepthTest')
    
    try:
        logger.info(f"测试获取{symbol}的深度数据...")
        
        result = client.get_depth(symbol, limit=5)
        
        logger.info("深度数据获取成功:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 解析买卖盘数据
        if 'bids' in result and result['bids']:
            logger.info(f"最高买价: {result['bids'][0]}")
        if 'asks' in result and result['asks']:
            logger.info(f"最低卖价: {result['asks'][0]}")
            
    except Exception as e:
        logger.error(f"深度测试失败: {e}")


def test_klines(client: BipcClient, symbol: str):
    """测试K线接口"""
    logger = get_logger('KlinesTest')
    
    try:
        logger.info(f"测试获取{symbol}的K线数据...")
        
        result = client.get_klines(symbol, interval='1m', limit=5)
        
        logger.info("K线数据获取成功:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 解析K线数据
        if isinstance(result, list) and result:
            latest_kline = result[-1]
            logger.info(f"最新K线数据: {latest_kline}")
            
    except Exception as e:
        logger.error(f"K线测试失败: {e}")


def test_custom_endpoint(client: BipcClient, endpoint: str, params: dict = None):
    """测试自定义端点"""
    logger = get_logger('CustomTest')
    
    try:
        logger.info(f"测试自定义端点: {endpoint}")
        
        # 直接使用_make_request方法测试
        result = client._make_request('GET', endpoint, params=params)
        
        logger.info("自定义端点测试成功:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))
        
    except Exception as e:
        logger.error(f"自定义端点测试失败: {e}")


if __name__ == "__main__":
    main()
