#!/usr/bin/env python3
"""
基于用户真实下单格式的测试
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from api.client import BipcClient


def test_real_user_format():
    """使用用户提供的真实下单格式"""
    print("🎯 使用用户真实下单格式测试")
    print("=" * 50)
    print("用户提供的真实格式:")
    print('{"symbol": "btc-usdt", "orderQty": 928, "side": 1, "type": "2", "source": 1}')
    print()
    print("参数分析:")
    print("- symbol: btc-usdt (小写，带连字符)")
    print("- orderQty: 928 (使用orderQty而不是quantity/volume!)")
    print("- side: 1 (数字，1可能=卖出)")
    print("- type: '2' (字符串，可能是市价单)")
    print("- source: 1 (新参数)")
    print("=" * 50)
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 测试BTC的小额订单
        test_order = {
            "symbol": "btc-usdt",     # 小写格式，使用BTC
            "orderQty": 1,            # 很小的数量
            "side": 2,                # 2=买入（推测）
            "type": "2",              # 市价单
            "source": 1               # 必需参数
        }
        
        print(f"测试订单: {test_order}")
        
        confirm = input("\n确认发送基于真实格式的测试订单？(yes/NO): ").strip()
        if confirm.lower() != 'yes':
            print("❌ 用户取消")
            return
        
        # 直接调用API
        url = client.endpoints.place_order
        result = client._make_request('POST', url, data=test_order, auth_required=True)
        
        print(f"\n结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('code') == 200:
            print("🎉 成功！找到正确格式")
            order_id = result.get('orderId')
            if order_id:
                print(f"📋 订单ID: {order_id}")
            return test_order
        elif result.get('code') == 403:
            print("⚠️  频率限制，但格式可能正确")
        elif result.get('code') == 500:
            print("❌ 仍然500错误")
        else:
            print(f"🔍 其他错误: {result.get('code')} - {result.get('message')}")
        
        return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None


def test_3000_usdt_real_format():
    """使用真实格式进行3000 USDT交易"""
    print("\n💰 使用真实格式进行3000 USDT交易")
    print("=" * 40)
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 计算BTC数量（基于3000 USDT）
        estimated_btc_price = 95000.0  # BTC价格约95000 USDT
        usdt_amount = 3000.0
        btc_qty = int(usdt_amount / estimated_btc_price * 100000)  # 转换为合适的orderQty单位

        print(f"计算参数:")
        print(f"- USDT金额: {usdt_amount}")
        print(f"- 估算BTC价格: {estimated_btc_price}")
        print(f"- 计算orderQty: {btc_qty}")

        # 买入订单（使用真实格式）
        buy_order = {
            "symbol": "btc-usdt",     # 小写，使用BTC
            "orderQty": btc_qty,      # 使用orderQty
            "side": 2,                # 2=买入
            "type": "2",              # 市价单
            "source": 1               # 必需参数
        }
        
        print(f"\n买入订单: {buy_order}")
        
        confirm = input("确认执行3000 USDT交易序列？(YES/no): ").strip()
        if confirm != 'YES':
            print("❌ 用户取消")
            return
        
        # 直接开始交易，不等待
        
        # 执行买入
        print("📈 执行买入...")
        url = client.endpoints.place_order
        buy_result = client._make_request('POST', url, data=buy_order, auth_required=True)
        
        print(f"买入结果: {json.dumps(buy_result, indent=2, ensure_ascii=False)}")
        
        if buy_result.get('code') == 200:
            print("✅ 买入成功！")
            buy_order_id = buy_result.get('orderId')
            
            # 等待10秒
            print("\n⏰ 等待10秒...")
            for i in range(10, 0, -1):
                print(f"   倒计时: {i} 秒", end='\r')
                time.sleep(1)
            print("\n")
            
            # 卖出订单
            sell_order = {
                "symbol": "btc-usdt",     # 小写，使用BTC
                "orderQty": btc_qty,      # 相同数量
                "side": 1,                # 1=卖出
                "type": "2",              # 市价单
                "source": 1               # 必需参数
            }
            
            print(f"📉 执行卖出...")
            print(f"卖出订单: {sell_order}")
            
            sell_result = client._make_request('POST', url, data=sell_order, auth_required=True)
            print(f"卖出结果: {json.dumps(sell_result, indent=2, ensure_ascii=False)}")
            
            if sell_result.get('code') == 200:
                print("✅ 卖出成功！")
                print("🎉 3000 USDT交易序列完成！")
                
                sell_order_id = sell_result.get('orderId')
                print(f"\n📋 交易总结:")
                print(f"买入订单ID: {buy_order_id}")
                print(f"卖出订单ID: {sell_order_id}")
                print(f"交易orderQty: {btc_qty}")
                print(f"交易金额: 约{usdt_amount} USDT")
            else:
                print(f"❌ 卖出失败: {sell_result.get('message')}")
        else:
            print(f"❌ 买入失败: {buy_result.get('message')}")
            
    except Exception as e:
        print(f"❌ 3000 USDT交易失败: {e}")


def test_different_sides():
    """测试不同的side值"""
    print("\n🔍 测试不同的side值")
    print("=" * 30)
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 测试side 1和2
        sides = [
            {"side": 1, "desc": "side=1 (可能是卖出)"},
            {"side": 2, "desc": "side=2 (可能是买入)"}
        ]
        
        for test in sides:
            print(f"\n🧪 测试 {test['desc']}")
            
            order = {
                "symbol": "btc-usdt",
                "orderQty": 1,        # 很小数量
                "side": test['side'],
                "type": "1",          # 尝试限价单
                "source": 1,
                "price": "1.0"        # 极低价格
            }
            
            print(f"订单: {order}")
            
            try:
                url = client.endpoints.place_order
                result = client._make_request('POST', url, data=order, auth_required=True)
                
                code = result.get('code')
                message = result.get('message', '')
                print(f"结果: {code} - {message}")
                
                if code == 200:
                    print(f"✅ side={test['side']} 成功！")
                    return test
                elif code == 403:
                    print(f"⚠️  频率限制")
                    
            except Exception as e:
                print(f"❌ 异常: {e}")
            
            time.sleep(5)
        
        return None
            
    except Exception as e:
        print(f"❌ side测试失败: {e}")
        return None


def main():
    """主函数"""
    print("🎯 基于用户真实下单格式的测试")
    print("=" * 60)
    print("关键发现：")
    print("✅ 参数名：orderQty (不是quantity或volume)")
    print("✅ 交易对：小写格式 btc-usdt")
    print("✅ 新参数：source=1")
    print("✅ side：数字格式 1/2")
    print("✅ type：字符串格式 '2'")
    print("=" * 60)
    
    while True:
        print("\n请选择测试:")
        print("1. 测试真实格式（小额订单）")
        print("2. 测试不同side值")
        print("3. 3000 USDT交易序列")
        print("4. 查询当前订单")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            successful_format = test_real_user_format()
            if successful_format:
                print(f"🎉 找到正确格式: {successful_format}")
        elif choice == '2':
            test_different_sides()
        elif choice == '3':
            test_3000_usdt_real_format()
        elif choice == '4':
            try:
                config = load_config('config.yaml')
                client = BipcClient(config)
                orders = client.get_open_orders()
                print("\n当前开放订单:")
                print(json.dumps(orders, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
