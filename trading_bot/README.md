# 自动交易机器人

这是一个用于 BIPC 交易所的自动交易机器人，支持算法交易和API接口调用。

## 项目结构

```
trading_bot/
├── README.md              # 项目说明文档
├── requirements.txt       # Python依赖包
├── config.yaml           # 配置文件
├── main.py               # 主程序入口
├── api/                  # API客户端模块
│   ├── __init__.py
│   ├── client.py         # API客户端类
│   └── endpoints.py      # API端点定义
├── strategies/           # 交易策略模块
│   ├── __init__.py
│   └── base_strategy.py  # 基础策略类
├── utils/               # 工具模块
│   ├── __init__.py
│   ├── logger.py        # 日志工具
│   └── config.py        # 配置管理
└── tests/               # 测试模块
    ├── __init__.py
    └── test_api.py      # API测试
```

## 功能特性

- [x] API接口封装
- [x] 实时市场数据获取
- [x] 自动下单功能
- [x] 算法交易策略
- [x] 风险控制
- [x] 日志记录
- [x] 配置管理

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置认证信息
编辑 `config.yaml` 文件，填入从浏览器获取的TOKEN：
```yaml
api:
  token: "你的TOKEN值"  # 从浏览器cookie中获取
```

### 3. 运行演示程序
```bash
python demo.py
```

### 4. 运行交易机器人
```bash
python main.py
```

详细使用说明请查看 [USAGE.md](USAGE.md)

## 注意事项

⚠️ **风险提示**：
- 本程序仅用于测试和学习目的
- 使用真实资金前请充分测试
- 交易有风险，投资需谨慎

## 已发现的API端点

### 私有API（需要TOKEN认证）
- `/swap/private/future/place_order` - **下单接口（核心功能）**
- `/swap/private/future/wallet/order/openOrders` - 获取开放订单
- `/swap/private/future/wallet/balance` - 获取账户余额
- `/trading-bots/futures/grid/private/strategy/info` - 获取网格策略信息
- `/swap/private/future/wallet/order/history` - 获取订单历史
- `/swap/private/future/wallet/order/cancel` - 撤销订单

### 认证方式
- 使用TOKEN cookie进行认证
- 需要特定的请求头（device-info, ppw等）

## 开发状态

项目开发完成，当前进度：
- [x] 项目结构设计
- [x] API接口分析
- [x] 基础客户端实现
- [x] 交易功能实现
- [x] 策略系统实现
- [x] 风险控制系统
- [x] 日志和配置管理
- [x] 演示和测试工具
