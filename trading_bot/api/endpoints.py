"""
BIPC交易所API端点定义
"""

from typing import Dict, Any


class BipcEndpoints:
    """BIPC交易所API端点类"""
    
    def __init__(self, base_url: str):
        """
        初始化API端点
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url.rstrip('/')
    
    # 市场数据相关端点
    @property
    def ticker(self) -> str:
        """获取ticker数据"""
        # 待分析后更新
        return f"{self.base_url}/api/v1/ticker"
    
    @property
    def depth(self) -> str:
        """获取订单簿深度"""
        # 待分析后更新
        return f"{self.base_url}/api/v1/depth"
    
    @property
    def klines(self) -> str:
        """获取K线数据"""
        # 待分析后更新
        return f"{self.base_url}/api/v1/klines"
    
    @property
    def trades(self) -> str:
        """获取最近交易记录"""
        # 待分析后更新
        return f"{self.base_url}/api/v1/trades"
    
    # 账户相关端点
    @property
    def account(self) -> str:
        """获取账户信息"""
        return f"{self.base_url}/swap/private/future/wallet/account"

    @property
    def balance(self) -> str:
        """获取账户余额"""
        return f"{self.base_url}/swap/private/future/wallet/balance"

    @property
    def position(self) -> str:
        """获取持仓信息"""
        return f"{self.base_url}/swap/private/future/wallet/position"
    
    # 交易相关端点
    @property
    def open_orders(self) -> str:
        """获取开放订单"""
        return f"{self.base_url}/swap/private/future/wallet/order/openOrders"

    @property
    def place_order(self) -> str:
        """下单接口"""
        return f"{self.base_url}/swap/private/future/place_order"

    @property
    def order(self) -> str:
        """查询订单"""
        return f"{self.base_url}/swap/private/future/wallet/order"

    @property
    def order_history(self) -> str:
        """查询订单历史"""
        return f"{self.base_url}/swap/private/future/wallet/order/history"

    @property
    def cancel_order(self) -> str:
        """撤销订单"""
        return f"{self.base_url}/swap/private/future/wallet/order/cancel"

    # 交易机器人相关端点
    @property
    def grid_strategy_info(self) -> str:
        """获取网格策略信息"""
        return f"{self.base_url}/trading-bots/futures/grid/private/strategy/info"

    @property
    def trading_bots_base(self) -> str:
        """交易机器人基础路径"""
        return f"{self.base_url}/trading-bots"
    
    # WebSocket端点
    @property
    def websocket(self) -> str:
        """WebSocket连接端点"""
        # 待分析后更新
        ws_url = self.base_url.replace('https://', 'wss://').replace('http://', 'ws://')
        return f"{ws_url}/ws"
    
    def get_symbol_ticker(self, symbol: str) -> str:
        """
        获取指定交易对的ticker数据
        
        Args:
            symbol: 交易对符号，如 'ETH-USDT'
            
        Returns:
            API端点URL
        """
        return f"{self.ticker}?symbol={symbol}"
    
    def get_symbol_depth(self, symbol: str, limit: int = 20) -> str:
        """
        获取指定交易对的订单簿深度
        
        Args:
            symbol: 交易对符号
            limit: 深度限制
            
        Returns:
            API端点URL
        """
        return f"{self.depth}?symbol={symbol}&limit={limit}"
    
    def get_symbol_klines(self, symbol: str, interval: str = '1m', limit: int = 100) -> str:
        """
        获取指定交易对的K线数据
        
        Args:
            symbol: 交易对符号
            interval: 时间间隔
            limit: 数据条数限制
            
        Returns:
            API端点URL
        """
        return f"{self.klines}?symbol={symbol}&interval={interval}&limit={limit}"
