"""
BIPC交易所API客户端
"""

import time
import hmac
import hashlib
import requests
import json
import base64
from typing import Dict, Any, Optional, Union
from urllib.parse import urlencode

from .endpoints import BipcEndpoints
from utils.logger import get_logger


class BipcClient:
    """BIPC交易所API客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化API客户端
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.api_config = config['api']
        self.base_url = self.api_config['base_url']
        self.api_key = self.api_config.get('api_key', '')
        self.api_secret = self.api_config.get('api_secret', '')
        self.token = self.api_config.get('token', '')
        self.jsessionid = self.api_config.get('jsessionid', '')
        self.timeout = self.api_config.get('timeout', 30)
        self.max_retries = self.api_config.get('max_retries', 3)
        
        # 初始化端点
        self.endpoints = BipcEndpoints(self.base_url)
        
        # 初始化HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-TW',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Referer': f'{self.base_url}/zh/swap/eth-usdt',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'ppw': 'W001'
        })
        
        # 日志记录器
        self.logger = get_logger('BipcClient')
        
        # 设置设备信息
        self._setup_device_info()

        # 自动设置认证信息（如果配置中有的话）
        token = self.api_config.get('token', '')
        jsessionid = self.api_config.get('jsessionid', '')
        if token:
            self.set_auth_token(token, jsessionid)

        self.logger.info(f"BIPC API客户端初始化完成，基础URL: {self.base_url}")

    def _setup_device_info(self) -> None:
        """设置设备信息"""
        device_info = {
            "device_id": "",
            "device_name": "chrome",
            "model": "web",
            "system_lang": "zh-CN",
            "system_version": "138.0",
            "timezone": "GMT+8",
            "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "platform": "macOS",
            "latlng": "",
            "fingerprint": "rd2h3GoSAtj3otBM8bYA",
            "requestId": f"{int(time.time() * 1000)}.zeLXSK"
        }

        # 将设备信息编码为base64
        device_info_json = json.dumps(device_info, separators=(',', ':'))
        device_info_b64 = base64.b64encode(device_info_json.encode('utf-8')).decode('utf-8')

        # 添加到请求头
        self.session.headers['device-info'] = device_info_b64

    def set_auth_token(self, token: str, jsessionid: str = None) -> None:
        """
        设置认证令牌

        Args:
            token: TOKEN值
            jsessionid: JSESSIONID值（可选）
        """
        # 设置TOKEN cookie
        self.session.cookies.set('TOKEN', token, domain='new-bipc-9.bydtms.com')

        if jsessionid:
            self.session.cookies.set('JSESSIONID', jsessionid, domain='new-bipc-9.bydtms.com')

        self.logger.info("认证令牌已设置")
    
    def _generate_signature(self, params: Dict[str, Any], timestamp: int) -> str:
        """
        生成API签名
        
        Args:
            params: 请求参数
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        if not self.api_secret:
            return ""
        
        # 这里需要根据实际的API签名规则进行调整
        # 目前是一个通用的HMAC-SHA256签名实现
        query_string = urlencode(sorted(params.items()))
        message = f"{timestamp}{query_string}"
        
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def _prepare_headers(self, params: Dict[str, Any] = None) -> Dict[str, str]:
        """
        准备请求头

        Args:
            params: 请求参数

        Returns:
            请求头字典
        """
        headers = {}

        # 基础请求头
        headers['accept'] = 'application/json, text/plain, */*'
        headers['accept-encoding'] = 'gzip, deflate, br, zstd'
        headers['accept-language'] = 'zh-TW'
        headers['cache-control'] = 'no-cache'
        headers['content-type'] = 'application/json'
        headers['origin'] = 'https://new-bipc-9.bydtms.com'
        headers['pragma'] = 'no-cache'
        headers['priority'] = 'u=1, i'
        headers['referer'] = 'https://new-bipc-9.bydtms.com/zh/swap/btc-usdt'
        headers['sec-ch-ua'] = '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'
        headers['sec-ch-ua-mobile'] = '?0'
        headers['sec-ch-ua-platform'] = '"macOS"'
        headers['sec-fetch-dest'] = 'empty'
        headers['sec-fetch-mode'] = 'cors'
        headers['sec-fetch-site'] = 'same-origin'
        headers['user-agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

        # 子钱包参数
        headers['ppw'] = 'W001'

        # 设备信息 (Base64编码的JSON)
        import base64
        import json
        device_info = {
            "device_id": "",
            "device_name": "chrome",
            "model": "web",
            "system_lang": "zh-CN",
            "system_version": "138.0",
            "timezone": "GMT+8",
            "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "platform": "macOS",
            "latlng": "",
            "fingerprint": "rd2h3GoSAtj3otBM8bYA",
            "requestId": f"{int(time.time() * 1000)}.{hash(str(time.time())) % 100000}"
        }
        device_info_json = json.dumps(device_info, separators=(',', ':'))
        device_info_b64 = base64.b64encode(device_info_json.encode('utf-8')).decode('utf-8')
        headers['device-info'] = device_info_b64

        # 添加Cookie信息
        if hasattr(self, 'token') and self.token:
            cookie_parts = [f"TOKEN={self.token}"]
            if hasattr(self, 'jsessionid') and self.jsessionid:
                cookie_parts.append(f"JSESSIONID={self.jsessionid}")

            # 添加其他必要的cookie
            cookie_parts.extend([
                "_ga=GA1.1.1158768195.1753780749",
                "agent=false",
                "user_origin=1",
                "_cfuvid=y6gXqPiTQKdFX4W5ESPRKVrq1_gahxnwgr3tCDwLPGw-1754012362270-0.0.1.1-604800000"
            ])

            headers['cookie'] = "; ".join(cookie_parts)

        if self.api_key:
            timestamp = int(time.time() * 1000)
            headers['X-API-KEY'] = self.api_key
            headers['X-TIMESTAMP'] = str(timestamp)

            if params and self.api_secret:
                signature = self._generate_signature(params, timestamp)
                headers['X-SIGNATURE'] = signature

        return headers
    
    def _make_request(
        self,
        method: str,
        url: str,
        params: Dict[str, Any] = None,
        data: Dict[str, Any] = None,
        auth_required: bool = False
    ) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            params: URL参数
            data: 请求体数据
            auth_required: 是否需要认证
            
        Returns:
            响应数据
        """
        if params is None:
            params = {}
        
        # 准备请求头
        headers = self.session.headers.copy()
        if auth_required:
            auth_headers = self._prepare_headers(params if method == 'GET' else data)
            headers.update(auth_headers)
        
        # 发送请求
        for attempt in range(self.max_retries + 1):
            try:
                self.logger.debug(f"发送{method}请求到: {url}")
                
                if method.upper() == 'GET':
                    response = self.session.get(
                        url,
                        params=params,
                        headers=headers,
                        timeout=self.timeout
                    )
                elif method.upper() == 'POST':
                    response = self.session.post(
                        url,
                        params=params,
                        json=data,
                        headers=headers,
                        timeout=self.timeout
                    )
                elif method.upper() == 'DELETE':
                    response = self.session.delete(
                        url,
                        params=params,
                        headers=headers,
                        timeout=self.timeout
                    )
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                # 检查响应状态
                response.raise_for_status()
                
                # 解析响应
                result = response.json()
                self.logger.debug(f"请求成功，响应: {result}")
                
                return result
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")
                
                if attempt == self.max_retries:
                    self.logger.error(f"请求最终失败: {e}")
                    raise
                
                # 等待后重试
                time.sleep(2 ** attempt)
    
    # 市场数据API方法
    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        获取ticker数据

        Args:
            symbol: 交易对符号

        Returns:
            ticker数据
        """
        # TODO: 更新为真实的API端点
        # url = self.endpoints.get_symbol_ticker(symbol)
        # return self._make_request('GET', url)

        # 临时模拟数据，等待真实API信息
        self.logger.warning("使用模拟ticker数据，请更新为真实API端点")
        return self._get_mock_ticker(symbol)

    def get_depth(self, symbol: str, limit: int = 20) -> Dict[str, Any]:
        """
        获取订单簿深度

        Args:
            symbol: 交易对符号
            limit: 深度限制

        Returns:
            深度数据
        """
        # TODO: 更新为真实的API端点
        # url = self.endpoints.get_symbol_depth(symbol, limit)
        # return self._make_request('GET', url)

        # 临时模拟数据，等待真实API信息
        self.logger.warning("使用模拟深度数据，请更新为真实API端点")
        return self._get_mock_depth(symbol, limit)

    def get_klines(self, symbol: str, interval: str = '1m', limit: int = 100) -> Dict[str, Any]:
        """
        获取K线数据

        Args:
            symbol: 交易对符号
            interval: 时间间隔
            limit: 数据条数

        Returns:
            K线数据
        """
        # TODO: 更新为真实的API端点
        # url = self.endpoints.get_symbol_klines(symbol, interval, limit)
        # return self._make_request('GET', url)

        # 临时模拟数据，等待真实API信息
        self.logger.warning("使用模拟K线数据，请更新为真实API端点")
        return self._get_mock_klines(symbol, interval, limit)

    # 交易相关API方法
    def get_open_orders(self, page: int = 1, size: int = 9999, all_orders: bool = True) -> Dict[str, Any]:
        """
        获取开放订单

        Args:
            page: 页码
            size: 每页数量
            all_orders: 是否获取所有订单

        Returns:
            开放订单数据
        """
        url = self.endpoints.open_orders
        params = {
            'page': page,
            'size': size,
            'all': str(all_orders).lower()
        }

        return self._make_request('GET', url, params=params, auth_required=True)

    def place_order(self, symbol: str = None, side: int = None, order_type: str = None,
                   quantity: float = None, price: float = None, orderQty: int = None,
                   source: int = 1, **kwargs) -> Dict[str, Any]:
        """
        下单 - 支持新的BIPC API格式

        Args:
            symbol: 交易对符号 (如 "btc-usdt")
            side: 买卖方向 (1=卖出, 2=买入)
            order_type: 订单类型 ("1"=限价, "2"=市价)
            quantity: 数量 (旧格式兼容)
            price: 价格（限价单必填）
            orderQty: 订单数量 (新格式)
            source: 来源 (默认1)
            **kwargs: 其他参数

        Returns:
            下单结果
        """
        url = self.endpoints.place_order

        # 支持新的BIPC API格式
        if 'symbol' in kwargs or symbol:
            # 使用新格式
            data = {
                'symbol': kwargs.get('symbol', symbol),
                'side': kwargs.get('side', side),
                'type': kwargs.get('type', order_type),
                'orderQty': kwargs.get('orderQty', orderQty or int(quantity or 1)),
                'source': kwargs.get('source', source)
            }

            if price is not None or 'price' in kwargs:
                data['price'] = str(kwargs.get('price', price))

        else:
            # 兼容旧格式
            data = {
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'quantity': str(quantity) if quantity else '1'
            }

            if price is not None:
                data['price'] = str(price)

        # 调试：打印实际发送的数据
        self.logger.info(f"发送下单请求数据: {data}")

        return self._make_request('POST', url, data=data, auth_required=True)

    def cancel_order(self, order_id: str, symbol: str = None) -> Dict[str, Any]:
        """
        撤销订单

        Args:
            order_id: 订单ID
            symbol: 交易对符号（可选）

        Returns:
            撤销结果
        """
        url = self.endpoints.cancel_order

        data = {'orderId': order_id}
        if symbol:
            data['symbol'] = symbol

        return self._make_request('POST', url, data=data, auth_required=True)

    def get_balance(self) -> Dict[str, Any]:
        """
        获取账户余额

        Returns:
            账户余额数据
        """
        url = self.endpoints.balance
        return self._make_request('GET', url, auth_required=True)

    def get_position(self) -> Dict[str, Any]:
        """
        获取持仓信息

        Returns:
            持仓数据
        """
        url = self.endpoints.position
        return self._make_request('GET', url, auth_required=True)

    def get_order_history(self, page: int = 1, size: int = 100) -> Dict[str, Any]:
        """
        获取订单历史

        Args:
            page: 页码
            size: 每页数量

        Returns:
            订单历史数据
        """
        url = self.endpoints.order_history
        params = {
            'page': page,
            'size': size
        }
        return self._make_request('GET', url, params=params, auth_required=True)

    def get_grid_strategy_info(self) -> Dict[str, Any]:
        """
        获取网格策略信息

        Returns:
            网格策略信息
        """
        url = self.endpoints.grid_strategy_info
        return self._make_request('GET', url, auth_required=True)

    # 模拟数据方法（用于测试）
    def _get_mock_ticker(self, symbol: str) -> Dict[str, Any]:
        """生成模拟ticker数据"""
        import random

        base_price = 3000.0 if 'ETH' in symbol.upper() else 50000.0
        price = base_price + random.uniform(-100, 100)

        return {
            'symbol': symbol,
            'price': f"{price:.2f}",
            'priceChange': f"{random.uniform(-50, 50):.2f}",
            'priceChangePercent': f"{random.uniform(-2, 2):.2f}",
            'volume': f"{random.uniform(1000, 10000):.2f}",
            'high': f"{price + random.uniform(0, 50):.2f}",
            'low': f"{price - random.uniform(0, 50):.2f}",
            'timestamp': int(time.time() * 1000)
        }

    def _get_mock_depth(self, symbol: str, limit: int) -> Dict[str, Any]:
        """生成模拟深度数据"""
        import random

        base_price = 3000.0 if 'ETH' in symbol.upper() else 50000.0

        bids = []
        asks = []

        for i in range(limit):
            bid_price = base_price - (i + 1) * 0.1
            ask_price = base_price + (i + 1) * 0.1

            bids.append([f"{bid_price:.2f}", f"{random.uniform(0.1, 10):.4f}"])
            asks.append([f"{ask_price:.2f}", f"{random.uniform(0.1, 10):.4f}"])

        return {
            'symbol': symbol,
            'bids': bids,
            'asks': asks,
            'timestamp': int(time.time() * 1000)
        }

    def _get_mock_klines(self, symbol: str, interval: str, limit: int) -> Dict[str, Any]:
        """生成模拟K线数据"""
        import random

        base_price = 3000.0 if 'ETH' in symbol.upper() else 50000.0
        klines = []

        current_time = int(time.time() * 1000)
        interval_ms = 60000  # 1分钟

        for i in range(limit):
            timestamp = current_time - (limit - i - 1) * interval_ms
            open_price = base_price + random.uniform(-50, 50)
            close_price = open_price + random.uniform(-10, 10)
            high_price = max(open_price, close_price) + random.uniform(0, 5)
            low_price = min(open_price, close_price) - random.uniform(0, 5)
            volume = random.uniform(100, 1000)

            klines.append([
                timestamp,
                f"{open_price:.2f}",
                f"{high_price:.2f}",
                f"{low_price:.2f}",
                f"{close_price:.2f}",
                f"{volume:.2f}"
            ])

        return {
            'symbol': symbol,
            'interval': interval,
            'data': klines
        }
