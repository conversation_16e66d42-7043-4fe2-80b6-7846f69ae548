#!/usr/bin/env python3
"""
检查订单状态
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import load_config
from api.client import BipcClient


def check_specific_order():
    """检查特定订单"""
    # 从日志中的订单ID
    order_ids = [
        "7356694600754806784",  # 买入订单ID
        "7356694643889029120"   # 卖出订单ID
    ]

    print(f"🔍 检查最近的交易订单")
    print("=" * 50)

    try:
        config = load_config('config.yaml')
        client = BipcClient(config)

        # 查询开放订单
        print("1. 查询开放订单...")
        open_orders = client.get_open_orders()

        found_orders = []
        if open_orders.get('data') and open_orders['data'].get('pageData'):
            for order in open_orders['data']['pageData']:
                order_id = order.get('orderId')
                if order_id in order_ids:
                    print(f"✅ 在开放订单中找到: {order_id}")
                    print(f"   交易对: {order.get('symbol')}")
                    print(f"   数量: {order.get('volume')}")
                    print(f"   价格: {order.get('price')}")
                    print(f"   状态: {order.get('status')}")
                    print(f"   方向: {order.get('side')}")
                    found_orders.append(order_id)

        if not found_orders:
            print("❌ 在开放订单中未找到目标订单")
            print("   可能原因：订单已成交或已取消")
        
        # 尝试查询历史订单
        print("\n2. 尝试查询历史订单...")
        
        # 可能的历史订单端点
        history_endpoints = [
            '/swap/private/future/wallet/order/historyOrders',
            '/swap/private/future/order/history',
            '/swap/private/order/history',
            '/api/order/history'
        ]
        
        for endpoint in history_endpoints:
            try:
                url = f"{client.base_url}{endpoint}"
                result = client._make_request('GET', url, auth_required=True)
                
                print(f"✅ {endpoint} 可访问")
                
                # 查找我们的订单
                if isinstance(result, dict) and result.get('data'):
                    orders_data = result['data']
                    if isinstance(orders_data, dict) and 'pageData' in orders_data:
                        orders_list = orders_data['pageData']
                    elif isinstance(orders_data, list):
                        orders_list = orders_data
                    else:
                        orders_list = []
                    
                    for order in orders_list[:20]:  # 检查前20个
                        order_id = order.get('orderId')
                        if order_id in order_ids:
                            print(f"🎯 在历史订单中找到: {order_id}")
                            print(f"   订单ID: {order.get('orderId')}")
                            print(f"   交易对: {order.get('symbol')}")
                            print(f"   数量: {order.get('volume')}")
                            print(f"   价格: {order.get('price')}")
                            print(f"   状态: {order.get('status')}")
                            print(f"   成交量: {order.get('dealVolume', 'N/A')}")
                            print(f"   成交价: {order.get('avgPrice', 'N/A')}")
                            print(f"   创建时间: {order.get('ctime', 'N/A')}")
                            print(f"   更新时间: {order.get('utime', 'N/A')}")
                            print("---")
                            found_orders.append(order_id)
                
                print(f"   前几个历史订单: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}...")
                break
                
            except Exception as e:
                print(f"❌ {endpoint} 失败: {str(e)[:100]}...")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")


def check_positions_and_balance():
    """检查持仓和余额"""
    print("\n💰 检查持仓和余额")
    print("=" * 30)

    try:
        config = load_config('config.yaml')
        client = BipcClient(config)

        # 检查余额
        print("1. 账户余额:")
        try:
            balance = client.get_balance()
            print(f"   余额数据: {json.dumps(balance, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"   ❌ 获取余额失败: {e}")

        # 检查持仓
        print("\n2. 持仓信息:")
        try:
            position = client.get_position()
            print(f"   持仓数据: {json.dumps(position, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"   ❌ 获取持仓失败: {e}")

    except Exception as e:
        print(f"❌ 检查失败: {e}")


def check_all_orders():
    """检查所有订单"""
    print("\n📋 检查所有订单")
    print("=" * 30)
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 开放订单
        print("开放订单:")
        open_orders = client.get_open_orders()
        
        if open_orders.get('data') and open_orders['data'].get('pageData'):
            for i, order in enumerate(open_orders['data']['pageData'][:5], 1):
                print(f"  {i}. ID: {order.get('orderId')}")
                print(f"     交易对: {order.get('symbol')}")
                print(f"     数量: {order.get('volume')}")
                print(f"     价格: {order.get('price')}")
                print(f"     状态: {order.get('status')}")
                print(f"     时间: {order.get('ctime')}")
                print()
        else:
            print("  无开放订单")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")


def analyze_order_quantity():
    """分析订单数量"""
    print("\n🧮 分析orderQty=1的实际含义")
    print("=" * 40)
    
    print("可能的解释:")
    print("1. orderQty=1 可能表示1个最小单位")
    print("2. 在期货交易中，可能是1张合约")
    print("3. 可能需要乘以某个倍数才是实际数量")
    print("4. 可能是以wei或其他小单位计算")
    print()
    print("建议:")
    print("- 检查交易所的API文档")
    print("- 查看合约规格")
    print("- 对比网页下单的参数")


def main():
    """主函数"""
    print("🔍 订单状态检查")
    print("=" * 40)
    print("目标：找出为什么网页上看不到订单")
    print("=" * 40)

    while True:
        print("\n请选择检查项目:")
        print("1. 检查特定订单状态")
        print("2. 检查所有订单")
        print("3. 检查持仓和余额")
        print("4. 分析数量问题")
        print("5. 全面检查")
        print("0. 退出")

        choice = input("\n请输入选择 (0-5): ").strip()

        if choice == '0':
            break
        elif choice == '1':
            check_specific_order()
        elif choice == '2':
            check_all_orders()
        elif choice == '3':
            check_positions_and_balance()
        elif choice == '4':
            analyze_order_quantity()
        elif choice == '5':
            # 全面检查
            check_specific_order()
            check_all_orders()
            check_positions_and_balance()
            analyze_order_quantity()

            print("\n💡 总结建议:")
            print("1. 检查网页的'历史订单'或'已完成订单'页面")
            print("2. 确认查看的是btc-usdt交易对（不是eth-usdt）")
            print("3. orderQty=3157可能对应很小的BTC数量")
            print("4. 市价单可能瞬间成交了")
            print("5. 检查是否在正确的交易账户（现货/期货）")
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
