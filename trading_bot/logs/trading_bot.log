2025-07-31 20:46:12 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 20:46:12 | INFO     | __main__:main:28 | === API接口测试开始 ===
2025-07-31 20:46:12 | INFO     | api.client:__init__:48 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 20:46:12 | INFO     | __main__:test_ticker:53 | 测试获取ETH-USDT的ticker数据...
2025-07-31 20:46:12 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 1/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/ticker?symbol=ETH-USDT
2025-07-31 20:46:13 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 2/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/ticker?symbol=ETH-USDT
2025-07-31 20:46:15 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 3/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/ticker?symbol=ETH-USDT
2025-07-31 20:46:20 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 4/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/ticker?symbol=ETH-USDT
2025-07-31 20:46:20 | ERROR    | api.client:_make_request:173 | 请求最终失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/ticker?symbol=ETH-USDT
2025-07-31 20:46:20 | ERROR    | __main__:test_ticker:69 | Ticker测试失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/ticker?symbol=ETH-USDT
2025-07-31 20:46:20 | INFO     | __main__:test_depth:77 | 测试获取ETH-USDT的深度数据...
2025-07-31 20:46:23 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 1/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/depth?symbol=ETH-USDT&limit=5
2025-07-31 20:46:26 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 2/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/depth?symbol=ETH-USDT&limit=5
2025-07-31 20:46:28 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 3/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/depth?symbol=ETH-USDT&limit=5
2025-07-31 20:46:32 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 4/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/depth?symbol=ETH-USDT&limit=5
2025-07-31 20:46:32 | ERROR    | api.client:_make_request:173 | 请求最终失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/depth?symbol=ETH-USDT&limit=5
2025-07-31 20:46:32 | ERROR    | __main__:test_depth:91 | 深度测试失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/depth?symbol=ETH-USDT&limit=5
2025-07-31 20:46:32 | INFO     | __main__:test_klines:99 | 测试获取ETH-USDT的K线数据...
2025-07-31 20:46:32 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 1/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/klines?symbol=ETH-USDT&interval=1m&limit=5
2025-07-31 20:46:33 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 2/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/klines?symbol=ETH-USDT&interval=1m&limit=5
2025-07-31 20:46:36 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 3/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/klines?symbol=ETH-USDT&interval=1m&limit=5
2025-07-31 20:46:40 | WARNING  | api.client:_make_request:170 | 请求失败 (尝试 4/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/klines?symbol=ETH-USDT&interval=1m&limit=5
2025-07-31 20:46:40 | ERROR    | api.client:_make_request:173 | 请求最终失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/klines?symbol=ETH-USDT&interval=1m&limit=5
2025-07-31 20:46:40 | ERROR    | __main__:test_klines:112 | K线测试失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/api/v1/klines?symbol=ETH-USDT&interval=1m&limit=5
2025-07-31 20:46:40 | INFO     | __main__:main:41 | === API接口测试完成 ===
2025-07-31 20:56:29 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 20:56:29 | INFO     | __main__:main:29 | === BIPC真实API测试开始 ===
2025-07-31 20:56:29 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:55 | 测试获取ETH-USDT的市场数据...
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:58 | 获取ticker数据...
2025-07-31 20:56:29 | WARNING  | api.client:get_ticker:254 | 使用模拟ticker数据，请更新为真实API端点
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:60 | Ticker数据:
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:61 | {
  "symbol": "ETH-USDT",
  "price": "2942.39",
  "priceChange": "-28.53",
  "priceChangePercent": "1.28",
  "volume": "4354.68",
  "high": "2953.01",
  "low": "2905.93",
  "timestamp": 1753966589342
}
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:64 | 获取深度数据...
2025-07-31 20:56:29 | WARNING  | api.client:get_depth:273 | 使用模拟深度数据，请更新为真实API端点
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:66 | 深度数据:
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:67 | {
  "symbol": "ETH-USDT",
  "bids": [
    [
      "2999.90",
      "3.8790"
    ],
    [
      "2999.80",
      "1.6613"
    ],
    [
      "2999.70",
      "1.2361"
    ],
    [
      "2999.60",
      "1.5306"
    ],
    [
      "2999.50",
      "4.4295"
    ]
  ],
  "asks": [
    [
      "3000.10",
      "3.6571"
    ],
    [
      "3000.20",
      "1.8835"
    ],
    [
      "3000.30",
      "2.0013"
    ],
    [
      "3000.40",
      "7.8037"
    ],
    [
      "3000.50",
      "5.1014"
    ]
  ],
  "timestamp": 1753966589342
}
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:70 | 获取K线数据...
2025-07-31 20:56:29 | WARNING  | api.client:get_klines:293 | 使用模拟K线数据，请更新为真实API端点
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:72 | K线数据:
2025-07-31 20:56:29 | INFO     | __main__:test_market_data:73 | {
  "symbol": "ETH-USDT",
  "interval": "1m",
  "data": [
    [
      1753966349343,
      "3043.81",
      "3046.43",
      "3039.29",
      "3042.05",
      "561.31"
    ],
    [
      1753966409343,
      "2993.85",
      "2998.57",
      "2985.83",
      "2987.62",
      "454.74"
    ],
    [
      1753966469343,
      "3048.49",
      "3052.60",
      "3045.70",
      "3047.16",
      "489.50"
    ],
    [
      1753966529343,
      "3021.34",
      "3022.69",
      "3008.29",
      "3012.98",
      "189.90"
    ],
    [
      1753966589343,
      "3023.08",
      "3032.96",
      "3022.78",
      "3029.38",
      "216.38"
    ]
  ]
}
2025-07-31 20:56:29 | INFO     | __main__:test_private_api:84 | 测试私有API...
2025-07-31 21:02:12 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 21:02:12 | INFO     | __main__:main:35 | === 交易机器人演示开始 ===
2025-07-31 21:02:12 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 21:06:53 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 21:06:53 | INFO     | __main__:main:28 | === 使用真实TOKEN测试API ===
2025-07-31 21:06:53 | INFO     | api.client:set_auth_token:107 | 认证令牌已设置
2025-07-31 21:06:53 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 21:06:53 | INFO     | api.client:set_auth_token:107 | 认证令牌已设置
2025-07-31 21:06:53 | INFO     | __main__:main:40 | 开始测试各种API端点...
2025-07-31 21:06:53 | INFO     | __main__:test_all_private_apis:56 | 1. 测试获取开放订单...
2025-07-31 21:06:54 | INFO     | __main__:test_all_private_apis:59 | 开放订单获取成功:
2025-07-31 21:06:54 | INFO     | __main__:test_all_private_apis:60 | {
  "code": 200,
  "message": "",
  "data": {
    "pageData": [],
    "currentPage": 1,
    "pageSize": 0,
    "totalCount": 0
  }
}
2025-07-31 21:06:54 | INFO     | __main__:test_all_private_apis:67 | 2. 测试获取账户余额...
2025-07-31 21:06:54 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 1/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/balance
2025-07-31 21:06:55 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 2/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/balance
2025-07-31 21:06:57 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 3/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/balance
2025-07-31 21:07:01 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 4/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/balance
2025-07-31 21:07:01 | ERROR    | api.client:_make_request:232 | 请求最终失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/balance
2025-07-31 21:07:01 | ERROR    | __main__:test_all_private_apis:73 | 获取账户余额失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/balance
2025-07-31 21:07:01 | INFO     | __main__:test_all_private_apis:78 | 3. 测试获取持仓信息...
2025-07-31 21:07:01 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 1/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/position
2025-07-31 21:07:02 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 2/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/position
2025-07-31 21:07:04 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 3/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/position
2025-07-31 21:07:08 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 4/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/position
2025-07-31 21:07:08 | ERROR    | api.client:_make_request:232 | 请求最终失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/position
2025-07-31 21:07:08 | ERROR    | __main__:test_all_private_apis:84 | 获取持仓信息失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/position
2025-07-31 21:07:08 | INFO     | __main__:test_all_private_apis:89 | 4. 测试获取订单历史...
2025-07-31 21:07:08 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 1/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/order/history?page=1&size=10
2025-07-31 21:07:09 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 2/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/order/history?page=1&size=10
2025-07-31 21:07:11 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 3/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/order/history?page=1&size=10
2025-07-31 21:07:16 | WARNING  | api.client:_make_request:229 | 请求失败 (尝试 4/4): 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/order/history?page=1&size=10
2025-07-31 21:07:16 | ERROR    | api.client:_make_request:232 | 请求最终失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/order/history?page=1&size=10
2025-07-31 21:07:16 | ERROR    | __main__:test_all_private_apis:95 | 获取订单历史失败: 404 Client Error:  for url: https://new-bipc-9.bydtms.com/swap/private/future/wallet/order/history?page=1&size=10
2025-07-31 21:07:16 | INFO     | __main__:test_all_private_apis:100 | 5. 测试获取网格策略信息...
2025-07-31 21:07:16 | INFO     | __main__:test_all_private_apis:103 | 网格策略信息获取成功:
2025-07-31 21:07:16 | INFO     | __main__:test_all_private_apis:104 | {
  "code": 200,
  "message": "",
  "data": false
}
2025-07-31 21:07:16 | INFO     | __main__:main:45 | === API测试完成 ===
2025-07-31 21:16:45 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 21:16:45 | INFO     | api.client:set_auth_token:107 | 认证令牌已设置
2025-07-31 21:16:45 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 21:20:13 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 21:20:13 | INFO     | api.client:set_auth_token:107 | 认证令牌已设置
2025-07-31 21:20:13 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 21:20:13 | INFO     | __main__:test_trading_sequence:72 | 开始交易序列测试...
2025-07-31 21:25:37 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 21:25:37 | INFO     | api.client:set_auth_token:107 | 认证令牌已设置
2025-07-31 21:25:37 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 21:25:37 | INFO     | __main__:test_trading_sequence:72 | 开始交易序列测试...
2025-07-31 21:25:37 | INFO     | api.client:place_order:350 | 发送下单请求数据: {'symbol': 'ETHUSDT', 'side': 'buy', 'type': 'market', 'quantity': '1.0'}
2025-07-31 23:05:22 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 23:05:22 | INFO     | api.client:set_auth_token:107 | 认证令牌已设置
2025-07-31 23:05:22 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 23:05:22 | INFO     | __main__:test_trading_sequence:72 | 开始交易序列测试...
2025-07-31 23:05:22 | INFO     | api.client:place_order:350 | 发送下单请求数据: {'symbol': 'ETHUSDT', 'side': 'buy', 'type': 'market', 'quantity': '1.0'}
2025-07-31 23:06:06 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-07-31 23:06:06 | INFO     | api.client:set_auth_token:107 | 认证令牌已设置
2025-07-31 23:06:06 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-07-31 23:06:38 | INFO     | __main__:execute_btc_3000_trading:73 | 准备买入BTC: {'symbol': 'btc-usdt', 'orderQty': 3157, 'side': 2, 'type': '2', 'source': 1}
2025-07-31 23:06:39 | INFO     | __main__:execute_btc_3000_trading:80 | 买入结果: {'code': 200, 'message': '', 'data': {'orderId': '7356701837669842944', 'hasCancelPopup': False}}
2025-07-31 23:06:49 | INFO     | __main__:execute_btc_3000_trading:115 | 准备卖出BTC: {'symbol': 'btc-usdt', 'orderQty': 3157, 'side': 1, 'type': '2', 'source': 1}
2025-07-31 23:06:49 | INFO     | __main__:execute_btc_3000_trading:121 | 卖出结果: {'code': 200, 'message': '', 'data': {'orderId': '7356701880233639936', 'hasCancelPopup': False}}
2025-07-31 23:06:49 | INFO     | __main__:execute_btc_3000_trading:154 | BTC 3000 USDT交易序列完成
2025-08-01 10:04:19 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-08-01 10:04:19 | INFO     | api.client:set_auth_token:107 | 认证令牌已设置
2025-08-01 10:04:19 | INFO     | api.client:__init__:68 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-08-01 10:04:54 | INFO     | __main__:execute_btc_3000_trading:74 | 准备买入BTC: {'symbol': 'btc-usdt', 'orderQty': 3157, 'side': 2, 'type': '2', 'source': 1, 'ppw': 'W001'}
2025-08-01 10:04:54 | INFO     | __main__:execute_btc_3000_trading:81 | 买入结果: {'code': 200, 'message': '', 'data': {'orderId': '7356867494554255360', 'hasCancelPopup': False}}
2025-08-01 10:05:04 | INFO     | __main__:execute_btc_3000_trading:117 | 准备卖出BTC: {'symbol': 'btc-usdt', 'orderQty': 3157, 'side': 1, 'type': '2', 'source': 1, 'ppw': 'W001'}
2025-08-01 10:05:04 | INFO     | __main__:execute_btc_3000_trading:123 | 卖出结果: {'code': 200, 'message': '', 'data': {'orderId': '7356867536979640320', 'hasCancelPopup': False}}
2025-08-01 10:05:04 | INFO     | __main__:execute_btc_3000_trading:156 | BTC 3000 USDT交易序列完成
2025-08-01 10:11:34 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-08-01 10:11:34 | INFO     | api.client:set_auth_token:109 | 认证令牌已设置
2025-08-01 10:11:34 | INFO     | api.client:__init__:70 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-08-01 10:11:38 | INFO     | __main__:quick_btc_trade:61 | 买入订单: {'symbol': 'btc-usdt', 'orderQty': 526, 'side': 2, 'type': '2', 'source': 1, 'ppw': 'W001'}
2025-08-01 10:11:39 | INFO     | __main__:quick_btc_trade:68 | 买入结果: {'code': 200, 'message': '', 'data': {'orderId': '7356869190617546752', 'hasCancelPopup': False}}
2025-08-01 10:11:49 | INFO     | __main__:quick_btc_trade:99 | 卖出订单: {'symbol': 'btc-usdt', 'orderQty': 526, 'side': 1, 'type': '2', 'source': 1, 'ppw': 'W001'}
2025-08-01 10:11:49 | INFO     | __main__:quick_btc_trade:105 | 卖出结果: {'code': 200, 'message': '', 'data': {'orderId': '7356869233529470976', 'hasCancelPopup': False}}
2025-08-01 10:11:49 | INFO     | __main__:quick_btc_trade:126 | 快速BTC交易完成: 买入7356869190617546752, 卖出7356869233529470976
2025-08-01 10:12:13 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-08-01 10:12:13 | INFO     | api.client:set_auth_token:109 | 认证令牌已设置
2025-08-01 10:12:13 | INFO     | api.client:__init__:70 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-08-01 10:12:52 | INFO     | __main__:quick_btc_trade:61 | 买入订单: {'symbol': 'btc-usdt', 'orderQty': 3157, 'side': 2, 'type': '2', 'source': 1, 'ppw': 'W001'}
2025-08-01 10:12:52 | INFO     | __main__:quick_btc_trade:68 | 买入结果: {'code': 200, 'message': '', 'data': {'orderId': '7356869500253650944', 'hasCancelPopup': False}}
2025-08-01 10:13:02 | INFO     | __main__:quick_btc_trade:99 | 卖出订单: {'symbol': 'btc-usdt', 'orderQty': 3157, 'side': 1, 'type': '2', 'source': 1, 'ppw': 'W001'}
2025-08-01 10:13:03 | INFO     | __main__:quick_btc_trade:105 | 卖出结果: {'code': 200, 'message': '', 'data': {'orderId': '7356869543215906816', 'hasCancelPopup': False}}
2025-08-01 10:13:03 | INFO     | __main__:quick_btc_trade:126 | 快速BTC交易完成: 买入7356869500253650944, 卖出7356869543215906816
2025-08-01 10:36:06 | INFO     | utils.logger:setup_logger:55 | 日志系统初始化完成
2025-08-01 10:36:06 | INFO     | api.client:set_auth_token:109 | 认证令牌已设置
2025-08-01 10:36:06 | INFO     | api.client:__init__:70 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-08-01 10:36:12 | INFO     | __main__:doge_trade_sequence:72 | 买入DOGE订单: {'symbol': 'doge-usdt', 'orderQty': 40, 'side': 2, 'type': '2', 'source': 1, 'ppw': 'W001'}
2025-08-01 10:36:12 | INFO     | __main__:doge_trade_sequence:79 | 买入结果: {'code': 200, 'message': '', 'data': {'orderId': '7356875371322949632', 'hasCancelPopup': False}}
2025-08-01 10:36:22 | INFO     | __main__:doge_trade_sequence:110 | 卖出DOGE订单: {'symbol': 'doge-usdt', 'orderQty': 40, 'side': 1, 'type': '2', 'source': 1, 'ppw': 'W001'}
2025-08-01 10:36:22 | INFO     | __main__:doge_trade_sequence:116 | 卖出结果: {'code': 200, 'message': '', 'data': {'orderId': '7356875413819637760', 'hasCancelPopup': False}}
2025-08-01 10:36:22 | INFO     | __main__:doge_trade_sequence:137 | DOGE交易完成: 买入7356875371322949632, 卖出7356875413819637760
2025-08-01 10:36:38 | INFO     | api.client:set_auth_token:109 | 认证令牌已设置
2025-08-01 10:36:38 | INFO     | api.client:__init__:70 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
2025-08-01 10:37:31 | INFO     | api.client:set_auth_token:109 | 认证令牌已设置
2025-08-01 10:37:31 | INFO     | api.client:__init__:70 | BIPC API客户端初始化完成，基础URL: https://new-bipc-9.bydtms.com
