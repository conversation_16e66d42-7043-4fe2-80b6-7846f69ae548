#!/usr/bin/env python3
"""
高级API发现脚本
基于常见的交易所API模式进行更深入的探测
"""

import requests
import json
import time
from typing import List, Dict, Any


class AdvancedAPIDiscovery:
    """高级API发现类"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': f'{base_url}/zh/swap/eth-usdt',
            'Origin': base_url,
        })
    
    def test_endpoint(self, path: str, params: Dict[str, Any] = None, method: str = 'GET') -> Dict[str, Any]:
        """测试API端点"""
        url = f"{self.base_url}{path}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=15)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=params, timeout=15)
            else:
                return {'error': f'Unsupported method: {method}'}
            
            result = {
                'url': url,
                'method': method,
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'content_type': response.headers.get('content-type', ''),
                'response_size': len(response.content)
            }
            
            # 尝试解析响应
            try:
                if 'application/json' in result['content_type'] or response.text.strip().startswith('{'):
                    result['json_data'] = response.json()
                else:
                    result['text_data'] = response.text[:200]
            except:
                result['text_data'] = response.text[:200]
            
            return result
            
        except Exception as e:
            return {
                'url': url,
                'method': method,
                'status_code': 0,
                'success': False,
                'error': str(e)
            }
    
    def discover_bipc_api(self) -> List[Dict[str, Any]]:
        """发现BIPC特定的API端点"""
        
        # 基于BIPC可能使用的API模式
        api_patterns = [
            # 可能的API基础路径
            ('/api/market/ticker', {}),
            ('/api/market/depth', {}),
            ('/api/market/kline', {}),
            ('/api/market/trades', {}),
            
            # 带版本号的
            ('/api/v1/market/ticker', {}),
            ('/api/v1/market/depth', {}),
            ('/api/v1/market/kline', {}),
            ('/api/v1/market/trades', {}),
            
            # 合约相关
            ('/api/contract/ticker', {}),
            ('/api/contract/depth', {}),
            ('/api/contract/kline', {}),
            ('/api/v1/contract/ticker', {}),
            ('/api/v1/contract/depth', {}),
            ('/api/v1/contract/kline', {}),
            
            # 永续合约
            ('/api/perpetual/ticker', {}),
            ('/api/perpetual/depth', {}),
            ('/api/perpetual/kline', {}),
            ('/api/v1/perpetual/ticker', {}),
            ('/api/v1/perpetual/depth', {}),
            ('/api/v1/perpetual/kline', {}),
            
            # 带参数的测试
            ('/api/market/ticker', {'symbol': 'ETHUSDT'}),
            ('/api/market/ticker', {'symbol': 'ETH-USDT'}),
            ('/api/market/ticker', {'symbol': 'eth_usdt'}),
            ('/api/v1/market/ticker', {'symbol': 'ETHUSDT'}),
            ('/api/v1/market/ticker', {'symbol': 'ETH-USDT'}),
            
            # 其他可能的路径
            ('/api/spot/ticker', {}),
            ('/api/futures/ticker', {}),
            ('/api/swap/ticker', {}),
            ('/api/data/ticker', {}),
            ('/api/public/ticker', {}),
            
            # WebSocket相关
            ('/api/ws', {}),
            ('/api/websocket', {}),
            ('/ws', {}),
            ('/websocket', {}),
            
            # 可能的内部API
            ('/internal/api/ticker', {}),
            ('/private/api/ticker', {}),
            ('/public/api/ticker', {}),
        ]
        
        results = []
        
        print(f"开始高级API探测，基础URL: {self.base_url}")
        print("=" * 80)
        
        for i, (pattern, params) in enumerate(api_patterns, 1):
            param_str = f"?{json.dumps(params)}" if params else ""
            print(f"[{i:2d}/{len(api_patterns)}] 测试: {pattern}{param_str}")
            
            result = self.test_endpoint(pattern, params)
            results.append(result)
            
            if result['success']:
                print(f"  ✅ 成功! 状态码: {result['status_code']}")
                if 'json_data' in result:
                    json_str = json.dumps(result['json_data'], ensure_ascii=False)
                    if len(json_str) > 150:
                        print(f"  📄 JSON响应: {json_str[:150]}...")
                    else:
                        print(f"  📄 JSON响应: {json_str}")
                elif 'text_data' in result:
                    print(f"  📄 文本响应: {result['text_data']}")
            elif result['status_code'] == 404:
                print(f"  ❌ 404 Not Found")
            elif result['status_code'] == 403:
                print(f"  🔒 403 Forbidden")
            elif result['status_code'] == 401:
                print(f"  🔑 401 Unauthorized")
            elif result['status_code'] == 0:
                print(f"  ⚠️  连接错误: {result.get('error', 'Unknown')}")
            else:
                print(f"  ⚠️  状态码: {result['status_code']}")
                if 'text_data' in result and result['text_data']:
                    print(f"  📄 响应: {result['text_data'][:100]}")
            
            time.sleep(0.3)  # 避免请求过快
        
        return results
    
    def test_graphql_api(self) -> Dict[str, Any]:
        """测试GraphQL API"""
        print("\n测试GraphQL API:")
        print("=" * 40)
        
        graphql_endpoints = [
            '/graphql',
            '/api/graphql',
            '/api/v1/graphql',
            '/query',
            '/api/query'
        ]
        
        # 简单的GraphQL查询
        query = {
            "query": "{ __schema { types { name } } }"
        }
        
        for endpoint in graphql_endpoints:
            print(f"测试GraphQL: {endpoint}")
            result = self.test_endpoint(endpoint, query, 'POST')
            
            if result['success']:
                print(f"  ✅ GraphQL端点发现!")
                if 'json_data' in result:
                    print(f"  📄 响应: {json.dumps(result['json_data'], ensure_ascii=False)[:200]}...")
                return result
            else:
                print(f"  ❌ 失败: {result['status_code']}")
        
        return {}
    
    def analyze_results(self, results: List[Dict[str, Any]]) -> None:
        """分析结果"""
        successful = [r for r in results if r['success']]
        
        print(f"\n分析结果:")
        print("=" * 80)
        print(f"总共测试: {len(results)} 个端点")
        print(f"成功响应: {len(successful)} 个")
        
        if successful:
            print(f"\n成功的端点:")
            for result in successful:
                print(f"  ✅ {result['method']} {result['url']}")
                if 'json_data' in result:
                    # 分析JSON结构
                    data = result['json_data']
                    if isinstance(data, dict):
                        if 'data' in data and isinstance(data['data'], dict):
                            print(f"     包含数据字段: {list(data['data'].keys())}")
                        elif 'result' in data:
                            print(f"     包含结果字段: {type(data['result'])}")
                        else:
                            print(f"     顶级字段: {list(data.keys())}")
        else:
            print("  ❌ 没有发现可用的API端点")


def main():
    """主函数"""
    base_url = "https://new-bipc-9.bydtms.com"
    
    discovery = AdvancedAPIDiscovery(base_url)
    
    # 发现API端点
    results = discovery.discover_bipc_api()
    
    # 测试GraphQL
    discovery.test_graphql_api()
    
    # 分析结果
    discovery.analyze_results(results)
    
    print("\n高级API发现完成!")


if __name__ == "__main__":
    main()
